@theme {
  --color-alpha-black-0: rgba(0, 0, 0, 0);
  --color-alpha-black-5: rgba(0, 0, 0, 0.05);
  --color-alpha-black-10: rgba(0, 0, 0, 0.1);
  --color-alpha-black-20: rgba(0, 0, 0, 0.2);
  --color-alpha-black-30: rgba(0, 0, 0, 0.3);
  --color-alpha-black-40: rgba(0, 0, 0, 0.4);
  --color-alpha-black-50: rgba(0, 0, 0, 0.5);
  --color-alpha-black-60: rgba(0, 0, 0, 0.6);
  --color-alpha-black-70: rgba(0, 0, 0, 0.7);
  --color-alpha-black-80: rgba(0, 0, 0, 0.8);
  --color-alpha-black-90: rgba(0, 0, 0, 0.9);
  --color-alpha-white-0: rgba(255, 255, 255, 0);
  --color-alpha-white-5: rgba(255, 255, 255, 0.05);
  --color-alpha-white-10: rgba(255, 255, 255, 0.1);
  --color-alpha-white-20: rgba(255, 255, 255, 0.2);
  --color-alpha-white-30: rgba(255, 255, 255, 0.3);
  --color-alpha-white-40: rgba(255, 255, 255, 0.4);
  --color-alpha-white-50: rgba(255, 255, 255, 0.5);
  --color-alpha-white-60: rgba(255, 255, 255, 0.6);
  --color-alpha-white-70: rgba(255, 255, 255, 0.7);
  --color-alpha-white-80: rgba(255, 255, 255, 0.8);
  --color-alpha-white-90: rgba(255, 255, 255, 0.9);
  --color-azure-1: rgba(251, 253, 255, 1);
  --color-azure-2: rgba(242, 249, 255, 1);
  --color-azure-3: rgba(234, 244, 255, 1);
  --color-azure-4: rgba(224, 240, 255, 1);
  --color-azure-5: rgba(213, 235, 255, 1);
  --color-azure-6: rgba(202, 230, 255, 1);
  --color-azure-7: rgba(192, 225, 255, 1);
  --color-azure-8: rgba(168, 216, 255, 1);
  --color-azure-9: rgba(145, 208, 255, 1);
  --color-azure-10: rgba(81, 192, 255, 1);
  --color-azure-11: rgba(0, 148, 213, 1);
  --color-azure-12: rgba(0, 124, 180, 1);
  --color-azure-13: rgba(0, 103, 155, 1);
  --color-azure-14: rgba(0, 82, 124, 1);
  --color-azure-15: rgba(0, 58, 90, 1);
  --color-azure-16: rgba(0, 33, 51, 1);
  --color-blue-1: rgba(252, 253, 255, 1);
  --color-blue-2: rgba(246, 248, 255, 1);
  --color-blue-3: rgba(240, 242, 255, 1);
  --color-blue-4: rgba(234, 237, 255, 1);
  --color-blue-5: rgba(226, 231, 255, 1);
  --color-blue-6: rgba(219, 225, 255, 1);
  --color-blue-7: rgba(213, 220, 255, 1);
  --color-blue-8: rgba(197, 208, 255, 1);
  --color-blue-9: rgba(186, 199, 255, 1);
  --color-blue-10: rgba(151, 173, 255, 1);
  --color-blue-11: rgba(65, 136, 255, 1);
  --color-blue-12: rgba(0, 113, 233, 1);
  --color-blue-13: rgba(0, 91, 211, 1);
  --color-blue-14: rgba(0, 66, 153, 1);
  --color-blue-15: rgba(0, 46, 106, 1);
  --color-blue-16: rgba(0, 22, 51, 1);
  --color-brand-primary---blue-0: rgba(255, 255, 255, 1);
  --color-brand-primary---blue-5: rgba(230, 241, 253, 1);
  --color-brand-primary---blue-10: rgba(204, 227, 251, 1);
  --color-brand-primary---blue-15: rgba(179, 213, 249, 1);
  --color-brand-primary---blue-20: rgba(153, 199, 247, 1);
  --color-brand-primary---blue-30: rgba(102, 171, 242, 1);
  --color-brand-primary---blue-40: rgba(51, 143, 238, 1);
  --color-brand-primary---blue-50: rgba(0, 115, 234, 1);
  --color-brand-primary---blue-60: rgba(0, 92, 187, 1);
  --color-brand-primary---blue-70: rgba(0, 69, 140, 1);
  --color-brand-primary---blue-80: rgba(0, 46, 94, 1);
  --color-brand-primary---blue-85: rgba(0, 35, 70, 1);
  --color-brand-primary---blue-90: rgba(0, 23, 47, 1);
  --color-brand-primary---blue-95: rgba(0, 12, 23, 1);
  --color-brand-primary---blue-100: rgba(0, 0, 0, 1);
  --color-brand-primary---orange-0: rgba(255, 255, 255, 1);
  --color-brand-primary---orange-5: rgba(255, 243, 230, 1);
  --color-brand-primary---orange-10: rgba(255, 231, 205, 1);
  --color-brand-primary---orange-15: rgba(255, 219, 180, 1);
  --color-brand-primary---orange-20: rgba(255, 207, 155, 1);
  --color-brand-primary---orange-30: rgba(254, 183, 105, 1);
  --color-brand-primary---orange-40: rgba(254, 159, 55, 1);
  --color-brand-primary---orange-50: rgba(254, 135, 5, 1);
  --color-brand-primary---orange-60: rgba(203, 108, 4, 1);
  --color-brand-primary---orange-70: rgba(152, 81, 3, 1);
  --color-brand-primary---orange-80: rgba(102, 54, 2, 1);
  --color-brand-primary---orange-85: rgba(0, 35, 70, 1);
  --color-brand-primary---orange-90: rgba(0, 23, 47, 1);
  --color-brand-primary---orange-95: rgba(0, 12, 23, 1);
  --color-brand-primary---orange-100: rgba(0, 0, 0, 1);
  --color-cyan-1: rgba(250, 255, 254, 1);
  --color-cyan-2: rgba(231, 252, 248, 1);
  --color-cyan-3: rgba(215, 251, 243, 1);
  --color-cyan-4: rgba(192, 249, 239, 1);
  --color-cyan-5: rgba(165, 247, 233, 1);
  --color-cyan-6: rgba(144, 244, 227, 1);
  --color-cyan-7: rgba(146, 237, 222, 1);
  --color-cyan-8: rgba(130, 227, 209, 1);
  --color-cyan-9: rgba(116, 219, 200, 1);
  --color-cyan-10: rgba(23, 199, 167, 1);
  --color-cyan-11: rgba(19, 163, 137, 1);
  --color-cyan-12: rgba(14, 132, 112, 1);
  --color-cyan-13: rgba(8, 107, 90, 1);
  --color-cyan-14: rgba(9, 83, 70, 1);
  --color-cyan-15: rgba(6, 56, 47, 1);
  --color-cyan-16: rgba(5, 36, 30, 1);
  --color-green-1: rgba(248, 255, 251, 1);
  --color-green-2: rgba(227, 255, 237, 1);
  --color-green-3: rgba(205, 254, 225, 1);
  --color-green-4: rgba(180, 254, 210, 1);
  --color-green-5: rgba(146, 254, 194, 1);
  --color-green-6: rgba(99, 253, 176, 1);
  --color-green-7: rgba(56, 250, 163, 1);
  --color-green-8: rgba(53, 238, 155, 1);
  --color-green-9: rgba(50, 225, 147, 1);
  --color-green-10: rgba(46, 211, 137, 1);
  --color-green-11: rgba(50, 160, 110, 1);
  --color-green-12: rgba(41, 132, 90, 1);
  --color-green-13: rgba(19, 111, 69, 1);
  --color-green-14: rgba(12, 81, 50, 1);
  --color-green-15: rgba(8, 61, 37, 1);
  --color-green-16: rgba(9, 42, 27, 1);
  --color-lime-1: rgba(250, 255, 250, 1);
  --color-lime-2: rgba(228, 255, 229, 1);
  --color-lime-3: rgba(208, 255, 209, 1);
  --color-lime-4: rgba(187, 254, 190, 1);
  --color-lime-5: rgba(157, 254, 160, 1);
  --color-lime-6: rgba(119, 254, 122, 1);
  --color-lime-7: rgba(56, 254, 62, 1);
  --color-lime-8: rgba(40, 242, 47, 1);
  --color-lime-9: rgba(37, 232, 43, 1);
  --color-lime-10: rgba(32, 207, 39, 1);
  --color-lime-11: rgba(24, 168, 29, 1);
  --color-lime-12: rgba(17, 135, 21, 1);
  --color-lime-13: rgba(12, 113, 15, 1);
  --color-lime-14: rgba(11, 85, 13, 1);
  --color-lime-15: rgba(3, 61, 5, 1);
  --color-lime-16: rgba(3, 33, 4, 1);
  --color-magenta-1: rgba(255, 253, 255, 1);
  --color-magenta-2: rgba(255, 245, 255, 1);
  --color-magenta-3: rgba(253, 239, 253, 1);
  --color-magenta-4: rgba(254, 231, 254, 1);
  --color-magenta-5: rgba(252, 223, 252, 1);
  --color-magenta-6: rgba(251, 215, 251, 1);
  --color-magenta-7: rgba(251, 207, 251, 1);
  --color-magenta-8: rgba(249, 190, 249, 1);
  --color-magenta-9: rgba(248, 177, 248, 1);
  --color-magenta-10: rgba(246, 141, 246, 1);
  --color-magenta-11: rgba(225, 86, 225, 1);
  --color-magenta-12: rgba(197, 48, 197, 1);
  --color-magenta-13: rgba(159, 38, 159, 1);
  --color-magenta-14: rgba(121, 26, 121, 1);
  --color-magenta-15: rgba(86, 16, 86, 1);
  --color-magenta-16: rgba(52, 6, 52, 1);
  --color-neutral-1: rgba(255, 255, 255, 1);
  --color-neutral-2: rgba(253, 253, 253, 1);
  --color-neutral-3: rgba(250, 250, 250, 1);
  --color-neutral-4: rgba(247, 247, 247, 1);
  --color-neutral-5: rgba(243, 243, 243, 1);
  --color-neutral-6: rgba(241, 241, 241, 1);
  --color-neutral-7: rgba(235, 235, 235, 1);
  --color-neutral-8: rgba(227, 227, 227, 1);
  --color-neutral-9: rgba(212, 212, 212, 1);
  --color-neutral-10: rgba(204, 204, 204, 1);
  --color-neutral-11: rgba(181, 181, 181, 1);
  --color-neutral-12: rgba(138, 138, 138, 1);
  --color-neutral-13: rgba(97, 97, 97, 1);
  --color-neutral-14: rgba(74, 74, 74, 1);
  --color-neutral-15: rgba(48, 48, 48, 1);
  --color-neutral-16: rgba(26, 26, 26, 1);
  --color-orange-1: rgba(255, 253, 250, 1);
  --color-orange-2: rgba(255, 247, 238, 1);
  --color-orange-3: rgba(255, 241, 227, 1);
  --color-orange-4: rgba(255, 235, 213, 1);
  --color-orange-5: rgba(255, 228, 198, 1);
  --color-orange-6: rgba(255, 221, 182, 1);
  --color-orange-7: rgba(255, 214, 164, 1);
  --color-orange-8: rgba(255, 200, 121, 1);
  --color-orange-9: rgba(255, 184, 0, 1);
  --color-orange-10: rgba(229, 165, 0, 1);
  --color-orange-11: rgba(178, 132, 0, 1);
  --color-orange-12: rgba(149, 111, 0, 1);
  --color-orange-13: rgba(124, 88, 0, 1);
  --color-orange-14: rgba(94, 66, 0, 1);
  --color-orange-15: rgba(65, 45, 0, 1);
  --color-orange-16: rgba(37, 26, 0, 1);
  --color-purple-1: rgba(253, 253, 255, 1);
  --color-purple-2: rgba(248, 247, 255, 1);
  --color-purple-3: rgba(243, 241, 255, 1);
  --color-purple-4: rgba(239, 236, 255, 1);
  --color-purple-5: rgba(233, 229, 255, 1);
  --color-purple-6: rgba(228, 222, 255, 1);
  --color-purple-7: rgba(223, 217, 255, 1);
  --color-purple-8: rgba(212, 204, 255, 1);
  --color-purple-9: rgba(199, 188, 255, 1);
  --color-purple-10: rgba(170, 149, 255, 1);
  --color-purple-11: rgba(148, 116, 255, 1);
  --color-purple-12: rgba(128, 81, 255, 1);
  --color-purple-13: rgba(113, 38, 255, 1);
  --color-purple-14: rgba(87, 0, 209, 1);
  --color-purple-15: rgba(59, 0, 147, 1);
  --color-purple-16: rgba(28, 0, 79, 1);
  --color-red-1: rgba(255, 251, 251, 1);
  --color-red-2: rgba(255, 246, 246, 1);
  --color-red-3: rgba(255, 237, 236, 1);
  --color-red-4: rgba(254, 233, 232, 1);
  --color-red-5: rgba(254, 226, 225, 1);
  --color-red-6: rgba(254, 218, 217, 1);
  --color-red-7: rgba(254, 211, 209, 1);
  --color-red-8: rgba(254, 195, 193, 1);
  --color-red-9: rgba(253, 176, 172, 1);
  --color-red-10: rgba(253, 129, 122, 1);
  --color-red-11: rgba(239, 77, 47, 1);
  --color-red-12: rgba(229, 28, 0, 1);
  --color-red-13: rgba(181, 38, 11, 1);
  --color-red-14: rgba(142, 31, 11, 1);
  --color-red-15: rgba(95, 21, 7, 1);
  --color-red-16: rgba(47, 10, 4, 1);
  --color-rose-1: rgba(255, 253, 253, 1);
  --color-rose-2: rgba(255, 246, 248, 1);
  --color-rose-3: rgba(255, 239, 243, 1);
  --color-rose-4: rgba(255, 232, 238, 1);
  --color-rose-5: rgba(255, 225, 232, 1);
  --color-rose-6: rgba(255, 217, 227, 1);
  --color-rose-7: rgba(254, 209, 221, 1);
  --color-rose-8: rgba(254, 193, 210, 1);
  --color-rose-9: rgba(254, 181, 202, 1);
  --color-rose-10: rgba(254, 142, 177, 1);
  --color-rose-11: rgba(253, 75, 146, 1);
  --color-rose-12: rgba(227, 12, 118, 1);
  --color-rose-13: rgba(185, 7, 95, 1);
  --color-rose-14: rgba(141, 4, 72, 1);
  --color-rose-15: rgba(100, 2, 49, 1);
  --color-rose-16: rgba(62, 1, 28, 1);
  --color-yellow-1: rgba(255, 253, 246, 1);
  --color-yellow-2: rgba(255, 248, 219, 1);
  --color-yellow-3: rgba(255, 244, 191, 1);
  --color-yellow-4: rgba(255, 239, 157, 1);
  --color-yellow-5: rgba(255, 235, 120, 1);
  --color-yellow-6: rgba(255, 230, 0, 1);
  --color-yellow-7: rgba(247, 223, 0, 1);
  --color-yellow-8: rgba(234, 211, 0, 1);
  --color-yellow-9: rgba(225, 203, 0, 1);
  --color-yellow-10: rgba(197, 178, 0, 1);
  --color-yellow-11: rgba(153, 138, 0, 1);
  --color-yellow-12: rgba(130, 117, 0, 1);
  --color-yellow-13: rgba(105, 95, 0, 1);
  --color-yellow-14: rgba(79, 71, 0, 1);
  --color-yellow-15: rgba(51, 46, 0, 1);
  --color-yellow-16: rgba(31, 28, 0, 1);
  --color-shadow-ambient-darker: rgba(0, 0, 0, 0.2);
  --color-shadow-ambient: rgba(0, 0, 0, 0.12);
  --color-shadow-key-darker: rgba(0, 0, 0, 0.24);
  --color-shadow-key: rgba(0, 0, 0, 0.14);
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);

  /* Mojo Muse Theme Colors as primitives */
  --color-background-mojo: rgb(255, 255, 255);
  --color-mojo-brown-1: rgb(175, 154, 128); /* #AF9A80 - primary, brown */
  --color-mojo-brown-2: rgba(
    227,
    202,
    165,
    1
  ); /* #CEAB93 - primary-light, brown-light */
  --color-mojo-brown-3: rgba(
    227,
    202,
    165,
    1
  ); /* #E3CAA5 - primary-lighter, brown-lighter */
  --color-mojo-brown-4: rgb(95, 74, 59); /* #5F4A3B - primary-dark */
  --color-mojo-brown-5: rgba(139, 112, 88, 1); /* #8B7058 - brown-dark */
  --color-mojo-bronw-6: rgb(94, 75, 62); /*5E4B3E*/
  --color-mojo-cream: rgba(255, 251, 241, 1); /* #FFFBF1 - secondary, cream */
  --color-mojo-cream-1: rgb(255, 251, 241); /* #FFFBE9 - secondary, cream */
  --color-mojo-cream-2: rgba(240, 240, 224, 1); /* #F0F0E0 - secondary-dark */
  --color-mojo-cream-3: rgba(255, 253, 242, 1); /* #FFFDF2 - cream-light */
  --color-mojo-cream-4: rgb(166, 148, 122); /*#A6947A*/
  --color-background-card: rgb(255, 251, 241); /*#FFFBF1*/
  --color-cancel: rgb(220, 38, 38); /*#DC2626*/
  --color-card-border: rgb(226, 232, 240); /*#E2E8F0*/
  --border-radius-none: 0px;
  --border-radius-050: 2px;
  --border-radius-100: 4px;
  --border-radius-150: 6px;
  --border-radius-200: 8px;
  --border-radius-300: 12px;
  --border-radius-400: 16px;
  --border-radius-500: 20px;
  --border-radius-750: 30px;
  --border-radius-circular: 9999px;

  /* Breakpoints (min-width based) */
  --breakpoint-sm: 0px; /* Small: 0–599px */
  --breakpoint-md: 600px; /* Medium: 600–839px */
  --breakpoint-lg: 840px; /* Large: 840–1199px */
  --breakpoint-xl: 1200px; /* Extra-large: 1200–1439px */
  --breakpoint-2xl: 1440px; /* Extra-largeExpanded: 1440+ */
  /* Margins per breakpoint */
  --margin-sm: 16px;
  --margin-md: 24px;
  --margin-lg: 32px;
  --margin-xl: 32px;
  --margin-2xl: 32px;

  /* Shadow (elevator) */
  --shadow-02:
    0px 1px 2px var(--color-shadow-key), 0px 0px 2px var(--color-shadow-ambient);

  --shadow-04:
    0px 2px 4px var(--color-shadow-key), 0px 0px 2px var(--color-shadow-ambient);

  --shadow-08:
    0px 4px 8px var(--color-shadow-key), 0px 0px 2px var(--color-shadow-ambient);

  --shadow-16:
    0px 8px 16px var(--color-shadow-key),
    0px 1px 2px var(--color-shadow-ambient);

  --shadow-28:
    0px 14px 28px var(--color-shadow-key-darker),
    0px 0px 8px var(--color-shadow-ambient-darker);

  --shadow-64:
    0px 32px 64px var(--color-shadow-key-darker),
    0px 0px 8px var(--color-shadow-ambient-darker);

  /* Positive Spacing Tokens */
  --space-none: 0px;
  --space-025: 1px;
  --space-050: 2px;
  --space-100: 4px;
  --space-150: 6px;
  --space-200: 8px;
  --space-300: 12px;
  --space-400: 16px;
  --space-500: 20px;
  --space-600: 24px;
  --space-800: 32px;
  --space-1000: 40px;
  --space-1200: 48px;
  --space-1600: 64px;
  --space-2000: 80px;
  --space-2400: 96px;
  --space-3200: 128px;

  /* Negative Spacing Tokens */
  --space-negative-25: -1px;
  --space-negative-50: -2px;
  --space-negative-100: -4px;
  --space-negative-150: -6px;
  --space-negative-200: -8px;
  --space-negative-300: -12px;

  /* Icon Size */
  --icon-size-012: 12px;
  --icon-size-016: 16px;
  --icon-size-020: 20px;
  --icon-size-024: 24px;
}
