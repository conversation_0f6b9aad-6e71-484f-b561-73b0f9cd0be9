import {
  AMEX_REGEX,
  JCB_REGEX,
  MASTERCARD_REGEX,
  NON_DIGIT_REGEX,
  THAI_MOBILE_REGEX,
  VISA_REGEX,
} from "@/constants/regex";

export const luhnCheck = (cardNumber: string): boolean => {
  const digits = cardNumber.replace(NON_DIGIT_REGEX, "");

  let sum = 0;
  let isEven = false;

  for (let i = digits.length - 1; i >= 0; i--) {
    let digit = parseInt(digits[i]);

    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    isEven = !isEven;
  }

  return sum % 10 === 0;
};

export const isNotExpired = (
  expiryMonth: string,
  expiryYear: string,
): boolean => {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1;
  const currentYear = currentDate.getFullYear();

  const month = parseInt(expiryMonth, 10);
  const year = parseInt(expiryYear, 10);

  if (year < currentYear || (year === currentYear && month < currentMonth)) {
    return false;
  }

  return true;
};

export const getCardBrand = (cardNumber: string): string => {
  const number = cardNumber.replace(NON_DIGIT_REGEX, "");

  if (VISA_REGEX.test(number)) return "visa";

  if (MASTERCARD_REGEX.test(number)) return "mastercard";

  if (JCB_REGEX.test(number)) return "jcb";

  if (AMEX_REGEX.test(number)) return "amex";

  return "unknown";
};

export const isTrueMoneyRegistered = async (
  phoneNumber: string,
): Promise<boolean> => {
  return THAI_MOBILE_REGEX.test(phoneNumber);
};
