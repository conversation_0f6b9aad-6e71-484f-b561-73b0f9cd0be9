import { useCallback, useEffect, useState } from "react";

import use<PERSON><PERSON> from "swr";

import { getMockAddressById, getMockAddresses } from "@/data/mockAddresses";
import { ApiService } from "@/services/api";
import { ADDRESS_ENDPOINTS } from "@/services/api/constants";
import type { Address, AddressFormData } from "@/types/address";

// For now, we'll use mock data. Later this can be replaced with actual API calls
const fetchAddresses = async (): Promise<Address[]> => {
  try {
    // Try to fetch from real API first
    const response = await ApiService.get<{ results: Address[] }>(
      ADDRESS_ENDPOINTS.ADDRESSES,
    );
    return response.results;
  } catch (error) {
    console.warn("Failed to fetch from API, using mock data:", error);
    // Fallback to mock data
    const response = await getMockAddresses();
    return response.results;
  }
};

export const useAddresses = () => {
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [defaultAddress, setDefaultAddress] = useState<Address | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const { data, error, mutate } = useSWR("/api/user/addresses", fetchAddresses);

  useEffect(() => {
    if (data) {
      const addressArray = Array.isArray(data) ? data : [];
      setAddresses(addressArray);
      setDefaultAddress(addressArray.find((addr) => addr.is_default) || null);
      setIsLoading(false);
    }
  }, [data]);

  const addAddress = useCallback(
    async (formData: AddressFormData): Promise<Address> => {
      try {
        // Try to create via API first
        const newAddress = await ApiService.post<Address>(
          ADDRESS_ENDPOINTS.ADDRESSES,
          formData,
        );
        mutate();
        return newAddress;
      } catch (error) {
        console.warn(
          "Failed to create via API, using mock implementation:",
          error,
        );
        // Fallback to mock implementation
        const newAddress: Address = {
          id: Math.max(...addresses.map((a) => a.id), 0) + 1,
          is_default: formData.is_default || false,
          recipient_name: formData.recipient_name,
          phone: formData.phone,
          address_line1: formData.address_line1,
          address_line2: formData.address_line2 || "",
          subdistrict: formData.subdistrict,
          district: formData.district,
          city: formData.city,
          province: formData.province,
          postal_code: formData.postal_code,
          country: formData.country,
          delivery_instructions: formData.delivery_instructions || "",
          full_address:
            `${formData.address_line1} ${formData.address_line2 || ""} ${formData.subdistrict} ${formData.district} ${formData.city} ${formData.province} ${formData.postal_code}`.trim(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        // If this is set as default, update other addresses
        if (newAddress.is_default) {
          addresses.forEach((addr) => (addr.is_default = false));
        }

        mutate();
        return newAddress;
      }
    },
    [addresses, mutate],
  );

  const updateAddress = useCallback(
    async (
      id: number,
      formData: Partial<AddressFormData>,
    ): Promise<Address> => {
      try {
        // Try to update via API first
        const updatedAddress = await ApiService.patch<Address>(
          ADDRESS_ENDPOINTS.ADDRESS_BY_ID(id),
          formData,
        );
        mutate();
        return updatedAddress;
      } catch (error) {
        console.warn(
          "Failed to update via API, using mock implementation:",
          error,
        );
        // Fallback to mock implementation
        const addressIndex = addresses.findIndex((addr) => addr.id === id);
        if (addressIndex === -1) {
          throw new Error("Address not found");
        }

        const updatedAddress: Address = {
          ...addresses[addressIndex],
          ...formData,
          full_address: formData.address_line1
            ? `${formData.address_line1} ${formData.address_line2 || ""} ${formData.subdistrict} ${formData.district} ${formData.city} ${formData.province} ${formData.postal_code}`.trim()
            : addresses[addressIndex].full_address,
          updated_at: new Date().toISOString(),
        };

        // If this is set as default, update other addresses
        if (updatedAddress.is_default) {
          addresses.forEach((addr) => {
            if (addr.id !== id) {
              addr.is_default = false;
            }
          });
        }

        mutate();
        return updatedAddress;
      }
    },
    [addresses, mutate],
  );

  const setAsDefault = useCallback(
    async (addressId: number) => {
      // Simulate API call - in real implementation, this would call the API
      addresses.forEach((addr) => {
        addr.is_default = addr.id === addressId;
      });
      mutate();
    },
    [addresses, mutate],
  );

  const removeAddress = useCallback(
    async (addressId: number) => {
      // Simulate API call - in real implementation, this would call the API
      const addressToRemove = addresses.find((addr) => addr.id === addressId);
      if (addressToRemove?.is_default && addresses.length > 1) {
        // If removing default address, set another one as default
        const otherAddress = addresses.find((addr) => addr.id !== addressId);
        if (otherAddress) {
          otherAddress.is_default = true;
        }
      }
      mutate();
    },
    [addresses, mutate],
  );

  const getAddress = useCallback(
    async (addressId: number): Promise<Address | null> => {
      try {
        return await getMockAddressById(addressId);
      } catch (error) {
        console.error("Error fetching address:", error);
        throw error;
      }
    },
    [],
  );

  return {
    addresses,
    defaultAddress,
    isLoading,
    error,
    addAddress,
    updateAddress,
    setAsDefault,
    removeAddress,
    getAddress,
    refresh: mutate,
  };
};
