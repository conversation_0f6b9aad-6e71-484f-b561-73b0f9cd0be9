/**
 * Types for Omise API responses
 */

/**
 * Omise Card object
 */
export interface OmiseCard {
  id: string;
  livemode: boolean;
  location: string;
  object: string;
  brand: string;
  bank: string;
  city: string;
  country: string;
  expiration_month: string;
  expiration_year: string;
  financing: string;
  fingerprint: string;
  first_digits: string;
  last_digits: string;
  name: string;
  phone_number: string | null;
  postal_code: string | null;
  security_code_check: boolean;
  created_at: string;
}

/**
 * Omise Cards List object
 */
export interface OmiseCardsList {
  object: string;
  data: OmiseCard[];
  limit: number;
  offset: number;
  total: number;
  location: string;
  order: string;
  from: string;
  to: string;
}

/**
 * Omise Customer object
 */
export interface OmiseCustomer {
  id: string;
  livemode: boolean;
  location: string;
  object: string;
  email: string;
  description: string;
  created_at: string;
  cards: OmiseCardsList;
  default_card: string | null;
  metadata: Record<string, string>;
}

/**
 * Omise Customers List object
 */
export interface OmiseCustomersList {
  object: string;
  data: OmiseCustomer[];
  limit: number;
  offset: number;
  total: number;
  location: string;
  order: string;
  from: string;
  to: string;
}
