import React from "react";
import { Controller, type Control, type FieldErrors } from "react-hook-form";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@base/card";
import { RadioGroup, RadioGroupItem } from "@base/radio-group";
import { Label } from "@base/label";
import { PAYMENT_METHODS, MO<PERSON>LE_BANKING_OPTIONS } from "@/pages/checkout/constants";
import type { CheckoutFormData } from "@/pages/checkout/constants";

interface PaymentMethodProps {
  control: Control<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
}

export const PaymentMethod: React.FC<PaymentMethodProps> = ({
  control,
  errors,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment</CardTitle>
        <p className="text-sm text-gray-500">
          การทำธุรกรรมทั้งหมดปลอดภัยและมีการเข้ารหัส
        </p>
      </CardHeader>
      <CardContent>
        <Controller
          name="paymentMethod"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value}
              onValueChange={(value) => {
                field.onChange(value);
              }}
            >
              {PAYMENT_METHODS.map((method) => (
                <Card 
                  key={method.id}
                  className={`cursor-pointer transition-colors ${
                    field.value === method.id ? 'border-primary bg-primary/5' : ''
                  }`}
                  onClick={() => {
                    field.onChange(method.id);
                  }}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value={method.id} id={method.id} />
                      <Label htmlFor={method.id} className="font-medium cursor-pointer">
                        {method.name}
                      </Label>
                    </div>
                    
                    {/* Mobile Banking Options */}
                    {field.value === method.id && method.id === 'mobile_banking' && (
                      <div className="mt-4 pl-7">
                        <p className="text-sm text-gray-600 mb-2">เลือกธนาคาร:</p>
                        <div className="grid grid-cols-2 gap-2">
                          {MOBILE_BANKING_OPTIONS.map((bank) => (
                            <div key={bank} className="text-sm p-2 bg-gray-50 rounded">
                              {bank}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </RadioGroup>
          )}
        />
        {errors.paymentMethod && (
          <p className="mt-2 text-xs text-red-600">
            {errors.paymentMethod.message}
          </p>
        )}
      </CardContent>
    </Card>
  );
};
