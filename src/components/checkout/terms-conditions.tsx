import React from "react";
import { Controller, type Control, type FieldErrors } from "react-hook-form";
import { Card, CardContent } from "@base/card";
import { Checkbox } from "@base/checkbox";
import { Label } from "@base/label";
import type { CheckoutFormData } from "@/pages/checkout/constants";

interface TermsConditionsProps {
  control: Control<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
}

export const TermsConditions: React.FC<TermsConditionsProps> = ({
  control,
  errors,
}) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          <p className="text-sm text-gray-600 leading-relaxed">
            การคลิกด้านล่างและดำเนินการสั่งซื้อของคุณ ถือว่าคุณตกลง (i) ที่จะทำการซื้อจาก Mojo Muse Management 
            ในฐานะผู้ค้าที่ได้รับการบันทึกไว้สำหรับธุรกรรมนี้ ภายใต้ข้อกำหนดและเงื่อนไขของ Mojo Muse Management; 
            (ii) ข้อมูลของคุณจะได้รับการจัดการโดย Mojo Muse Management ตามนโยบายความเป็นส่วนตัวของ Mojo Muse Management; 
            และ (iii) ข้อมูลของคุณ (ยกเว้นรายละเอียดการชำระเงิน) จะถูกแบ่งปันกับ Mojo Muse Management
          </p>

          <Controller
            name="agreeToTerms"
            control={control}
            render={({ field }) => (
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="agree-terms"
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  className="mt-1"
                />
                <Label htmlFor="agree-terms" className="text-sm leading-relaxed cursor-pointer">
                  การดำเนินการต่อแสดงว่าคุณยอมรับข้อกำหนดในการให้บริการของ Mojo Muse Management 
                  และรับทราบตามนโยบายความเป็นส่วนตัว
                </Label>
              </div>
            )}
          />
          {errors.agreeToTerms && (
            <p className="text-xs text-red-600">
              {errors.agreeToTerms.message}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
