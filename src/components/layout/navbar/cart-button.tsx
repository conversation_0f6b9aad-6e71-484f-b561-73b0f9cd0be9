import { ShoppingCartIcon } from "lucide-react";
import { useTranslation } from "@hooks/useTranslation";
import type { CartButtonProps } from "./types";

const CartButton = ({ onClick }: CartButtonProps = {}) => {
  const { t } = useTranslation();

  return (
    <div
      className="flex cursor-pointer items-center space-x-2 py-2 text-sm font-medium md:px-4"
      onClick={onClick}
    >
      <ShoppingCartIcon />
      <div className="md:hidden">
        <span className="text-[length:var(--font-size-md)] font-medium">
          {t("navbar.cart")}
        </span>
      </div>
    </div>
  );
};

export default CartButton;
