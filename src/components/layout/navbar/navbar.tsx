import { Link, useNavigate } from "react-router-dom";
import LanguageSwitcher from "@/components/language-switcher";
import { useScrollDetection, useMenuAnimation } from "./hooks";
import NavLinks from "./nav-links";
import LoginButton from "./login-button";
import { useTranslation } from "@/hooks/useTranslation";
import { Menu, X } from "lucide-react";
import CartButton from "@components/layout/navbar/cart-button.tsx";
import SearchButton from "@components/layout/navbar/search-button.tsx";

const Navbar = () => {
  const isScrolled = useScrollDetection();
  const {
    menuOpen: isMobileMenuOpen,
    toggleMenu: setIsMobileMenuOpen,
    mobileMenuRef,
  } = useMenuAnimation();
  useTranslation();
  const navigate = useNavigate();
  const navigateMobile = (path: string) => {
    navigate(path);
    setIsMobileMenuOpen();
  };
  return (
    <>
      {/* Desktop navbar */}
      <nav
        className={`fixed z-40 w-[95%] transform transition-all duration-500 ${
          "left-1/2 hidden -translate-x-1/2 lg:block " +
          (isScrolled ? "top-6" : "top-8")
        }`}
      >
        <div
          className={`bg-primary-lighter/80 rounded-[40px] px-[46px] py-[16px] drop-shadow-lg backdrop-blur-[10px] transition-all duration-500 ${
            isScrolled ? "shadow-lg shadow-purple-500/10" : ""
          }`}
        >
          <div className="flex items-center">
            {/* Logo */}
            <Link to="/" className="mr-10 flex items-center hover:scale-105">
              <div className="flex h-[32px] w-[88px] items-center justify-center">
                <img
                  src="/images/logo.png"
                  alt="Mojo Muse Logo"
                  onError={(e) => {
                    e.currentTarget.src = "/vite.svg";
                  }}
                />
              </div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden items-center space-x-6 lg:flex">
              <NavLinks />
            </div>

            {/* Right side items */}
            <div className="ml-auto hidden items-center space-x-3 lg:flex">
              <SearchButton />
              <button onClick={() => navigate("/shopping-cart")}>
                <CartButton />
              </button>
              <LoginButton />
              <div className="md:px-4">
                <div className="bg-cream h-6 w-px"></div>
              </div>
              <div className="md:px-4">
                <LanguageSwitcher />
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Unified Mobile navbar and menu */}
      <div className="lg:hidden">
        {/* Mobile navbar - always visible */}
        <nav
          className={`fixed top-0 right-0 left-0 z-50 transition-all duration-300 ${
            isMobileMenuOpen
              ? "bg-transparent"
              : "bg-primary-lighter/80 rounded-b-[40px] drop-shadow-lg backdrop-blur-[10px]"
          }`}
        >
          <div
            className={`px-[46px] py-[16px] ${isMobileMenuOpen ? "opacity-0" : "opacity-100"} transition-opacity duration-300`}
          >
            <div className="flex items-center">
              {/* Logo */}
              <Link to="/" className="mr-10 flex items-center hover:scale-105">
                <div className="flex h-[32px] w-[88px] items-center justify-center">
                  <img
                    src="/images/logo.png"
                    alt="Mojo Muse Logo"
                    onError={(e) => {
                      e.currentTarget.src = "/vite.svg";
                    }}
                  />
                </div>
              </Link>

              {/* Mobile Menu Button */}
              <div className="ml-auto flex items-center space-x-3 lg:flex">
                <LanguageSwitcher />
                <SearchButton />
                <button
                  onClick={() => setIsMobileMenuOpen()}
                  className="glass magnetic ml-auto rounded-full p-2 transition-colors hover:bg-white/10"
                >
                  {isMobileMenuOpen ? (
                    <X className="h-5 w-5 text-black" />
                  ) : (
                    <Menu />
                  )}
                </button>
              </div>
            </div>
          </div>
        </nav>

        {/* Mobile Menu - Full screen */}
        <div
          ref={mobileMenuRef}
          className="bg-primary-lighter/95 fixed inset-0 z-40 hidden overflow-y-auto backdrop-blur-[10px]"
        >
          <div className="flex min-h-screen flex-col">
            {/* Menu header with close button */}
            <div className="flex items-center px-[46px] py-[16px]">
              <Link to="/" className="mr-10 flex items-center hover:scale-105">
                <div className="flex h-[32px] w-[88px] items-center justify-center">
                  <img
                    src="/images/logo.png"
                    alt="Mojo Muse Logo"
                    onError={(e) => {
                      e.currentTarget.src = "/vite.svg";
                    }}
                  />
                </div>
              </Link>

              <button
                onClick={() => setIsMobileMenuOpen()}
                className="glass magnetic ml-auto rounded-full p-2 transition-colors hover:bg-white/10"
              >
                <X className="h-5 w-5 text-black" />
              </button>
            </div>

            {/* Menu content */}
            <div className="flex flex-col justify-center space-y-8 px-[46px] py-[24px]">
              <div className="space-y-8">
                <NavLinks mobile onClick={() => setIsMobileMenuOpen()} />
                <CartButton onClick={() => navigateMobile("/shopping-cart")} />
                <LoginButton onClick={() => setIsMobileMenuOpen()} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Navbar;
