import React from "react";
import { useNavigate } from "react-router-dom";

interface ProductCardProps {
  id: number;
  name: string;
  image: string;
  short_text: string;
  width?: number;
  height?: number;
}

const ProductCard: React.FC<ProductCardProps> = ({
  id,
  name,
  image,
  short_text,
  width = 72,
  height = 64,
}) => {
  const navigate = useNavigate();

  return (
    <div
      onClick={() => navigate(`/products/${id}`)}
      className={`w-${width} h-${height * 1.5} flex cursor-pointer flex-col overflow-hidden rounded-xl border border-slate-300 bg-white p-3 transition-shadow duration-300 hover:shadow-lg`}
    >
      <div
        className={`h-${height} w-${width * 0.8} overflow-hidden rounded-lg bg-gray-100`}
      >
        <img
          src={image}
          alt={name}
          className="h-full w-full object-cover transition-transform duration-300 ease-in-out hover:scale-105"
        />
      </div>

      <div className="flex-grow pt-4 text-left">
        <div>
          <h3 className="truncate text-xl font-bold text-slate-800">{name}</h3>
        </div>
        <p className="mt-1 truncate text-base font-light text-slate-600">
          {short_text}
        </p>
      </div>
    </div>
  );
};

export default ProductCard;
