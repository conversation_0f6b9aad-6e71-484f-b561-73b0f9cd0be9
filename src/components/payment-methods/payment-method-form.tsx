import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Footer,
} from "@/components/commons/base/card";
import { Button } from "@/components/commons/base/button";
import { Label } from "@/components/commons/base/label";
import { InputField } from "@/components/commons/form/input-field";
import { useTranslation } from "@/hooks/useTranslation";
import { XIcon, CreditCard, Eye, EyeOff } from "lucide-react";
import {
  CREDIT_CARD_LENGTH_REGEX,
  EXPIRY_MONTH_REGEX,
  CVV_REGEX,
  PHONE_NUMBER_REGEX,
  EMAIL_VALIDATION_REGEX,
  SPACE_REGEX,
  EXPIRY_DATE_VALIDATION_REGEX,
} from "@/constants/regex";
import type {
  AlipayFormData,
  CreditCardFormData,
  TrueMoneyFormData,
} from "@/types/payment-methods";
import { PaymentMethodType } from "@/types/payment-methods";
import type { PaymentMethodFormProps } from "./types";
import {
  luh<PERSON><PERSON><PERSON><PERSON>,
  isNot<PERSON>x<PERSON>,
  get<PERSON><PERSON><PERSON><PERSON>,
  isTrueM<PERSON><PERSON>egistered,
} from "@/utils/validation";
import { omiseTokenizationService } from "@/services/payment/omise-tokenization";
import {
  formatCreditCard,
  formatExpiryDate as maskExpiryDate,
  parseExpiryDate,
  formatMonthYearToExpiryDate,
} from "@/utils/mask";

export const PaymentMethodForm: React.FC<PaymentMethodFormProps> = ({
  type,
  initialData,
  isEdit,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation();
  const [showCVV, setShowCVV] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [creditCardForm, setCreditCardForm] = useState<CreditCardFormData>({
    holderName: "",
    cardNumber: "",
    expiryMonth: "",
    expiryYear: "",
    expiryDate: "",
    cvv: "",
    isDefault: false,
    displayName: "",
    omiseToken: "",
  });

  const [trueMoneyForm, setTrueMoneyForm] = useState<TrueMoneyFormData>({
    phoneNumber: "",
    isDefault: false,
    displayName: "",
  });

  const [alipayForm, setAlipayForm] = useState<AlipayFormData>({
    accountEmail: "",
    isDefault: false,
    displayName: "",
  });

  const [detectedCardBrand, setDetectedCardBrand] = useState<string>("");

  useEffect(() => {
    if (initialData) {
      switch (type) {
        case PaymentMethodType.CREDIT_CARD:
        case PaymentMethodType.DEBIT_CARD:
          setCreditCardForm((prev) => {
            const newData = {
              ...prev,
              ...initialData,
            };

            const cardData = initialData as Partial<CreditCardFormData>;
            if (
              cardData.expiryMonth &&
              cardData.expiryYear &&
              !cardData.expiryDate
            ) {
              newData.expiryDate = formatMonthYearToExpiryDate(
                cardData.expiryMonth,
                cardData.expiryYear,
              );
            }

            return newData;
          });

          if ((initialData as Partial<CreditCardFormData>).cardNumber) {
            setDetectedCardBrand(
              getCardBrand(
                (initialData as Partial<CreditCardFormData>).cardNumber || "",
              ),
            );
          }
          break;
        case PaymentMethodType.TRUEMONEY:
          setTrueMoneyForm((prev) => ({
            ...prev,
            ...initialData,
          }));
          break;
        case PaymentMethodType.ALIPAY:
          setAlipayForm((prev) => ({
            ...prev,
            ...initialData,
          }));
          break;
      }
    }
  }, [initialData, type]);

  const handleCreditCardChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type: inputType } = e.target;

    setCreditCardForm((prev) => {
      const newData = {
        ...prev,
        [name]: inputType === "checkbox" ? checked : value,
      };

      if (name === "cardNumber") {
        const formattedValue = formatCreditCard(value);
        newData.cardNumber = formattedValue;
        setDetectedCardBrand(getCardBrand(formattedValue));
      } else if (name === "expiryDate") {
        // Apply masking and auto-completion to expiryDate
        newData.expiryDate = maskExpiryDate(value);

        // When expiryDate changes, update expiryMonth and expiryYear
        const { month, year } = parseExpiryDate(newData.expiryDate);
        newData.expiryMonth = month;
        newData.expiryYear = year;
      } else if (name === "expiryMonth" || name === "expiryYear") {
        // When expiryMonth or expiryYear changes, update expiryDate
        const month = name === "expiryMonth" ? value : prev.expiryMonth;
        const year = name === "expiryYear" ? value : prev.expiryYear;
        newData.expiryDate = formatMonthYearToExpiryDate(month, year);
      }

      return newData;
    });
  };

  const handleTrueMoneyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type: inputType } = e.target;

    setTrueMoneyForm((prev) => ({
      ...prev,
      [name]: inputType === "checkbox" ? checked : value,
    }));
  };

  const handleAlipayChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type: inputType } = e.target;

    setAlipayForm((prev) => ({
      ...prev,
      [name]: inputType === "checkbox" ? checked : value,
    }));
  };

  const handleClearInput = (formType: PaymentMethodType, field: string) => {
    switch (formType) {
      case PaymentMethodType.CREDIT_CARD:
      case PaymentMethodType.DEBIT_CARD:
        setCreditCardForm((prev) => {
          const newData = {
            ...prev,
            [field]: "",
          };

          if (field === "cardNumber") {
            setDetectedCardBrand("");
          } else if (field === "expiryDate") {
            // When clearing expiryDate, also clear expiryMonth and expiryYear
            newData.expiryMonth = "";
            newData.expiryYear = "";
          } else if (field === "expiryMonth" || field === "expiryYear") {
            // When clearing expiryMonth or expiryYear, update expiryDate
            const month = field === "expiryMonth" ? "" : prev.expiryMonth;
            const year = field === "expiryYear" ? "" : prev.expiryYear;
            newData.expiryDate = formatMonthYearToExpiryDate(month, year);
          }

          return newData;
        });
        break;
      case PaymentMethodType.TRUEMONEY:
        setTrueMoneyForm((prev) => ({
          ...prev,
          [field]: "",
        }));
        break;
      case PaymentMethodType.ALIPAY:
        setAlipayForm((prev) => ({
          ...prev,
          [field]: "",
        }));
        break;
    }
  };

  const toggleCVVVisibility = () => {
    setShowCVV(!showCVV);
  };

  const validateCreditCardForm = async (): Promise<boolean> => {
    const newErrors: Record<string, string> = {};

    if (!creditCardForm.holderName) {
      newErrors.holderName = t(
        "payment.methods.form.validation.holderNameRequired",
      );
    }

    if (!creditCardForm.cardNumber) {
      newErrors.cardNumber = t(
        "payment.methods.form.validation.cardNumberRequired",
      );
    } else {
      const cleanNumber = creditCardForm.cardNumber.replace(SPACE_REGEX, "");
      if (!CREDIT_CARD_LENGTH_REGEX.test(cleanNumber)) {
        newErrors.cardNumber = t(
          "payment.methods.form.validation.cardNumberFormat",
        );
      } else if (!luhnCheck(cleanNumber)) {
        newErrors.cardNumber = t(
          "payment.methods.form.validation.cardNumberInvalid",
        );
      }
    }

    // Validate expiryDate field
    if (!creditCardForm.expiryDate) {
      newErrors.expiryDate = t(
        "payment.methods.form.validation.expiryDateRequired",
      );
    } else if (!EXPIRY_DATE_VALIDATION_REGEX.test(creditCardForm.expiryDate)) {
      newErrors.expiryDate = t(
        "payment.methods.form.validation.expiryDateFormat",
      );
    } else {
      // If expiryDate format is valid, check if the date is not expired
      const { month, year } = parseExpiryDate(creditCardForm.expiryDate);

      if (!EXPIRY_MONTH_REGEX.test(month)) {
        newErrors.expiryDate = t(
          "payment.methods.form.validation.expiryMonthInvalid",
        );
      } else if (!year || year.length !== 4) {
        newErrors.expiryDate = t(
          "payment.methods.form.validation.expiryYearInvalid",
        );
      } else if (!isNotExpired(month, year)) {
        newErrors.expiryDate = t(
          "payment.methods.form.validation.expiryInvalid",
        );
      }
    }

    if (!creditCardForm.cvv) {
      newErrors.cvv = t("payment.methods.form.validation.cvvRequired");
    } else if (!CVV_REGEX.test(creditCardForm.cvv)) {
      newErrors.cvv = t("payment.methods.form.validation.cvvFormat");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateTrueMoneyForm = async (): Promise<boolean> => {
    const newErrors: Record<string, string> = {};

    if (!trueMoneyForm.phoneNumber) {
      newErrors.phoneNumber = t(
        "payment.methods.form.validation.phoneNumberRequired",
      );
    } else if (!PHONE_NUMBER_REGEX.test(trueMoneyForm.phoneNumber)) {
      newErrors.phoneNumber = t(
        "payment.methods.form.validation.phoneNumberFormat",
      );
    } else {
      const isRegistered = await isTrueMoneyRegistered(
        trueMoneyForm.phoneNumber,
      );
      if (!isRegistered) {
        newErrors.phoneNumber = t(
          "payment.methods.form.validation.phoneNumberInvalid",
        );
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateAlipayForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (
      alipayForm.accountEmail &&
      !EMAIL_VALIDATION_REGEX.test(alipayForm.accountEmail)
    ) {
      newErrors.accountEmail = t("payment.methods.form.validation.emailFormat");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    let isValid = false;

    switch (type) {
      case PaymentMethodType.CREDIT_CARD:
      case PaymentMethodType.DEBIT_CARD:
        isValid = await validateCreditCardForm();
        if (isValid) {
          try {
            // Make sure expiryMonth and expiryYear are set from expiryDate if needed
            if (
              creditCardForm.expiryDate &&
              (!creditCardForm.expiryMonth || !creditCardForm.expiryYear)
            ) {
              const { month, year } = parseExpiryDate(
                creditCardForm.expiryDate,
              );
              if (!creditCardForm.expiryMonth) {
                creditCardForm.expiryMonth = month;
              }
              if (!creditCardForm.expiryYear) {
                creditCardForm.expiryYear = year;
              }
            }

            // Create token using Omise.js
            const token = await omiseTokenizationService.createCardToken({
              holderName: creditCardForm.holderName,
              cardNumber: creditCardForm.cardNumber,
              expiryMonth: creditCardForm.expiryMonth,
              expiryYear: creditCardForm.expiryYear,
              cvv: creditCardForm.cvv,
            });

            // Pass the token instead of the full card data
            const tokenizedCardForm = {
              ...creditCardForm,
              omiseToken: token,
              cardNumber: undefined,
              cvv: undefined,
            };

            onSubmit(type, tokenizedCardForm);
          } catch (error) {
            console.error("Error creating card token:", error);
            setErrors({
              ...errors,
              cardNumber: t(
                "payment.methods.form.validation.tokenizationError",
                {
                  error: (error as Error).message,
                },
              ),
            });
          }
        }
        break;
      case PaymentMethodType.TRUEMONEY:
        isValid = await validateTrueMoneyForm();
        if (isValid) {
          onSubmit(type, trueMoneyForm);
        }
        break;
      case PaymentMethodType.ALIPAY:
        isValid = validateAlipayForm();
        if (isValid) {
          onSubmit(type, alipayForm);
        }
        break;
    }
  };

  const renderCreditCardForm = () => (
    <>
      <div className="mb-4">
        <InputField
          id="holderName"
          name="holderName"
          label={t("payment.methods.form.holderName")}
          value={creditCardForm.holderName}
          onChange={handleCreditCardChange}
          placeholder={t("payment.methods.form.placeholders.holderName")}
          error={errors.holderName}
          className="relative pr-10"
        />
        {creditCardForm.holderName && (
          <button
            type="button"
            onClick={() => handleClearInput(type, "holderName")}
            className="absolute top-[calc(1.4/2*100%)] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            aria-label="Clear holder name"
          >
            <XIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      <div className="relative mb-4">
        <InputField
          id="cardNumber"
          name="cardNumber"
          label={t("payment.methods.form.cardNumber")}
          value={creditCardForm.cardNumber}
          onChange={handleCreditCardChange}
          placeholder={t("payment.methods.form.placeholders.cardNumber")}
          error={errors.cardNumber}
          className="pr-20"
          maxLength={19}
        />
        {creditCardForm.cardNumber && (
          <button
            type="button"
            onClick={() => handleClearInput(type, "cardNumber")}
            className="absolute top-[calc(1.4/2*100%)] right-10 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            aria-label="Clear card number"
          >
            <XIcon className="h-4 w-4" />
          </button>
        )}
        {detectedCardBrand && detectedCardBrand !== "unknown" && (
          <div className="absolute top-[calc(1.4/2*100%)] right-3 -translate-y-1/2">
            <img
              src={`src/assets/images/payments/${detectedCardBrand}.png`}
              alt={t(`payment.methods.altText.${detectedCardBrand}`)}
              className="h-6 w-6 object-contain"
            />
          </div>
        )}
      </div>

      <div className="relative mb-4">
        <InputField
          id="expiryDate"
          name="expiryDate"
          label={t("payment.methods.form.expiryDate")}
          value={creditCardForm.expiryDate}
          onChange={handleCreditCardChange}
          placeholder={t("payment.methods.form.placeholders.expiryDate")}
          error={errors.expiryDate}
          className="pr-10"
          maxLength={5}
        />
        {creditCardForm.expiryDate && (
          <button
            type="button"
            onClick={() => handleClearInput(type, "expiryDate")}
            className="absolute top-[calc(1.4/2*100%)] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            aria-label="Clear expiry date"
          >
            <XIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      <div className="relative mb-4">
        <InputField
          id="cvv"
          name="cvv"
          type={showCVV ? "text" : "password"}
          label={t("payment.methods.form.cvv")}
          value={creditCardForm.cvv}
          onChange={handleCreditCardChange}
          placeholder={t("payment.methods.form.placeholders.cvv")}
          error={errors.cvv}
          className="pr-20"
          maxLength={4}
        />
        <button
          type="button"
          onClick={toggleCVVVisibility}
          className="absolute top-[calc(1.4/2*100%)] right-10 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
          aria-label={showCVV ? "Hide CVV" : "Show CVV"}
        >
          {showCVV ? (
            <EyeOff className="h-4 w-4" />
          ) : (
            <Eye className="h-4 w-4" />
          )}
        </button>
        {creditCardForm.cvv && (
          <button
            type="button"
            onClick={() => handleClearInput(type, "cvv")}
            className="absolute top-[calc(1.4/2*100%)] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            aria-label="Clear CVV"
          >
            <XIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      <div className="relative mb-4">
        <InputField
          id="displayName"
          name="displayName"
          label={t("payment.methods.form.displayName")}
          value={creditCardForm.displayName}
          onChange={handleCreditCardChange}
          placeholder={t("payment.methods.form.placeholders.displayName")}
          className="pr-10"
        />
        {creditCardForm.displayName && (
          <button
            type="button"
            onClick={() => handleClearInput(type, "displayName")}
            className="absolute top-[calc(1.4/2*100%)] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            aria-label="Clear display name"
          >
            <XIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      <div className="mb-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isDefault"
            name="isDefault"
            checked={creditCardForm.isDefault}
            onChange={handleCreditCardChange}
            className="mr-2 h-4 w-4"
          />
          <Label htmlFor="isDefault">
            {t("payment.methods.form.isDefault")}
          </Label>
        </div>
      </div>
    </>
  );

  const renderTrueMoneyForm = () => (
    <>
      <div className="relative mb-4">
        <InputField
          id="phoneNumber"
          name="phoneNumber"
          label={t("payment.methods.form.phoneNumber")}
          value={trueMoneyForm.phoneNumber}
          onChange={handleTrueMoneyChange}
          placeholder={t("payment.methods.form.placeholders.phoneNumber")}
          error={errors.phoneNumber}
          className="pr-10"
          maxLength={10}
        />
        {trueMoneyForm.phoneNumber && (
          <button
            type="button"
            onClick={() => handleClearInput(type, "phoneNumber")}
            className="absolute top-[calc(1.4/2*100%)] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            aria-label="Clear phone number"
          >
            <XIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      <div className="relative mb-4">
        <InputField
          id="displayName"
          name="displayName"
          label={t("payment.methods.form.displayName")}
          value={trueMoneyForm.displayName}
          onChange={handleTrueMoneyChange}
          placeholder={t("payment.methods.form.placeholders.displayName")}
          className="pr-10"
        />
        {trueMoneyForm.displayName && (
          <button
            type="button"
            onClick={() => handleClearInput(type, "displayName")}
            className="absolute top-[calc(1.4/2*100%)] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            aria-label="Clear display name"
          >
            <XIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      <div className="mb-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isDefault"
            name="isDefault"
            checked={trueMoneyForm.isDefault}
            onChange={handleTrueMoneyChange}
            className="mr-2 h-4 w-4"
          />
          <Label htmlFor="isDefault">
            {t("payment.methods.form.isDefault")}
          </Label>
        </div>
      </div>
    </>
  );

  const renderAlipayForm = () => (
    <>
      <div className="relative mb-4">
        <InputField
          id="accountEmail"
          name="accountEmail"
          label={t("payment.methods.form.email")}
          value={alipayForm.accountEmail || ""}
          onChange={handleAlipayChange}
          placeholder={t("payment.methods.form.placeholders.email")}
          error={errors.accountEmail}
          className="pr-10"
        />
        {alipayForm.accountEmail && (
          <button
            type="button"
            onClick={() => handleClearInput(type, "accountEmail")}
            className="absolute top-[calc(1.4/2*100%)] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            aria-label="Clear account email"
          >
            <XIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      <div className="relative mb-4">
        <InputField
          id="displayName"
          name="displayName"
          label={t("payment.methods.form.displayName")}
          value={alipayForm.displayName || ""}
          onChange={handleAlipayChange}
          placeholder={t("payment.methods.form.placeholders.displayName")}
          className="pr-10"
        />
        {alipayForm.displayName && (
          <button
            type="button"
            onClick={() => handleClearInput(type, "displayName")}
            className="absolute top-[calc(1.4/2*100%)] right-3 -translate-y-1/2 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
            aria-label="Clear display name"
          >
            <XIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      <div className="mb-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isDefault"
            name="isDefault"
            checked={alipayForm.isDefault}
            onChange={handleAlipayChange}
            className="mr-2 h-4 w-4"
          />
          <Label htmlFor="isDefault">
            {t("payment.methods.form.isDefault")}
          </Label>
        </div>
      </div>
    </>
  );

  const renderFormFields = () => {
    switch (type) {
      case PaymentMethodType.CREDIT_CARD:
      case PaymentMethodType.DEBIT_CARD:
        return renderCreditCardForm();
      case PaymentMethodType.TRUEMONEY:
        return renderTrueMoneyForm();
      case PaymentMethodType.ALIPAY:
        return renderAlipayForm();
      default:
        return null;
    }
  };

  const getFormTitle = () => {
    const action = isEdit
      ? t("payment.methods.edit")
      : t("payment.methods.create");

    switch (type) {
      case PaymentMethodType.CREDIT_CARD:
        return `${action} - ${t("payment.methods.types.credit")}`;
      case PaymentMethodType.DEBIT_CARD:
        return `${action} - ${t("payment.methods.types.credit")}`;
      case PaymentMethodType.TRUEMONEY:
        return `${action} - ${t("payment.methods.types.truemoney")}`;
      case PaymentMethodType.ALIPAY:
        return `${action} - ${t("payment.methods.types.alipay")}`;
      default:
        return action;
    }
  };

  const getFormIcon = () => {
    switch (type) {
      case PaymentMethodType.CREDIT_CARD:
      case PaymentMethodType.DEBIT_CARD:
        return detectedCardBrand && detectedCardBrand !== "unknown" ? (
          <img
            src={`src/assets/images/payments/${detectedCardBrand}.png`}
            alt={t(`payment.methods.altText.${detectedCardBrand}`)}
            className="mr-2 h-8 w-8"
          />
        ) : (
          <CreditCard className="mr-2 h-6 w-6" />
        );
      case PaymentMethodType.TRUEMONEY:
        return (
          <img
            src="src/assets/images/payments/truemoney.png"
            alt={t("payment.methods.altText.truemoney")}
            className="mr-2 h-8 w-8"
          />
        );
      case PaymentMethodType.ALIPAY:
        return (
          <img
            src="src/assets/images/payments/alipay.png"
            alt={t("payment.methods.altText.alipay")}
            className="mr-2 h-6 w-6"
          />
        );
      default:
        return null;
    }
  };

  return (
    <Card className="w-full border-none">
      <CardHeader>
        <CardTitle className="font-regular flex items-center text-[23px] text-black">
          {getFormIcon()}
          {getFormTitle()}
        </CardTitle>
      </CardHeader>
      <CardContent className="lg:w-1/2">
        <form onSubmit={handleSubmit}>
          {renderFormFields()}

          <CardFooter className="flex justify-end space-x-2 px-0">
            <Button variant="outline" onClick={onCancel}>
              {t("payment.methods.cancel")}
            </Button>
            <Button type="submit">
              {isEdit
                ? t("payment.methods.save")
                : t("payment.methods.addButton")}
            </Button>
          </CardFooter>
        </form>
      </CardContent>
    </Card>
  );
};
