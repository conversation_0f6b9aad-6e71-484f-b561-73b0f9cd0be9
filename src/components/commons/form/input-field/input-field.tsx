import { cn } from "@/lib/utils";
import { Input } from "@commons/base";
import type { InputFieldProps } from "@commons/form/input-field/types.ts";

const InputField = ({
  id,
  label,
  type = "text",
  error,
  className,
  autoComplete,
  ...props
}: InputFieldProps) => {
  return (
    <div className="mb-4">
      <label htmlFor={id} className="mb-1 block text-sm font-medium text-black">
        {label}
      </label>
      <Input
        id={id}
        type={type}
        autoComplete={autoComplete}
        className={cn(
          error
            ? "border-red-500 focus:border-red-500 focus:ring-red-500"
            : "focus:border-primary-stroke-2-rest focus:ring-primary-stroke-1-rest border-gray-300",
          "bg-white",
          className,
        )}
        aria-invalid={!!error}
        {...props}
      />
      {error && <p className="mt-1 text-xs text-red-600">{error}</p>}
    </div>
  );
};

export default InputField;
