import * as React from "react";

export interface TabItem {
  value: string;
  label: React.ReactNode;
  disabled?: boolean;
  iconBefore?: React.ReactNode;
  iconAfter?: React.ReactNode;
}

export interface TabsProps {
  items: TabItem[];
  defaultValue?: string;
  value?: string;
  activeTab?: string;
  className?: string;
  tabsListClassName?: string;
  tabsTriggerClassName?: string;
  tabsContentClassName?: string;
  onChange?: (value: string) => void;
  children?: React.ReactNode;
  orientation?: "horizontal" | "vertical";
  renderTabButton?: (
    item: TabItem,
    isActive: boolean,
    onClick: () => void,
  ) => React.ReactNode;
  renderTabContent?: (activeTab: string) => React.ReactNode;
}

export interface TabsChildProps {
  className?: string;
  activeTab?: string;
  value?: string;
  [key: string]: unknown;
}
