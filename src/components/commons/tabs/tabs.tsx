import * as React from "react";
import {
  Tabs as BaseTabs,
  TabsList as BaseTabsList,
  TabsTrigger as BaseTabsTrigger,
  TabsContent as BaseTabsContent,
} from "@/components/commons/base/tabs";
import { cn } from "@/lib/utils";
import type { TabsChildProps, TabsProps } from "./types";

export function Tabs({
  items,
  defaultValue,
  value,
  activeTab,
  className,
  tabsListClassName,
  tabsTriggerClassName,
  tabsContentClassName,
  onChange,
  renderTabButton,
  renderTabContent,
  orientation = "horizontal",
  children,
}: TabsProps) {
  const defaultTab = defaultValue || (items.length > 0 ? items[0].value : "");
  const currentTab = activeTab || value || defaultTab;

  const isVertical = orientation === "vertical";

  const containerClasses = isVertical
    ? "flex flex-col md:flex-row"
    : "flex flex-col";

  const tabsListContainerClasses = isVertical
    ? "mb-4 w-full md:mb-0 md:max-w-[260px] md:flex-col md:space-y-2 md:pr-4"
    : "mb-4 w-full";

  const tabsListClasses = cn(
    "flex w-full",
    isVertical
      ? "flex-row overflow-x-auto md:flex-col md:space-y-2 md:overflow-visible"
      : "flex-row overflow-x-auto",
    "scrollbar-hide rounded-lg p-1",
    isVertical
      ? "md:border-1 border-primary-border-card rounded-[16px] md:border-0"
      : "",
    tabsListClassName,
  );

  const defaultTabButtonClasses = cn(
    "flex-shrink-0",
    isVertical
      ? "flex items-center justify-center p-2 md:w-full md:justify-start md:px-4 md:py-2 md:font-regular md:rounded-md md:text-sm"
      : "text-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium",
    "cursor-pointer transition-all duration-200",
  );

  return (
    <div className={cn("w-full", className)}>
      <div className={containerClasses}>
        <div className={tabsListContainerClasses}>
          <div className={tabsListClasses} role="tablist">
            {items.map((item) => {
              const isActive = currentTab === item.value;
              const handleClick = () => onChange && onChange(item.value);

              if (renderTabButton) {
                return (
                  <React.Fragment key={item.value}>
                    {renderTabButton(item, isActive, handleClick)}
                  </React.Fragment>
                );
              }

              return (
                <button
                  key={item.value}
                  role="tab"
                  aria-selected={isActive}
                  disabled={item.disabled}
                  className={cn(
                    defaultTabButtonClasses,
                    isActive
                      ? isVertical
                        ? "bg-primary text-white"
                        : "bg-white shadow-sm"
                      : "hover:bg-gray-200",
                    item.value === "logout" && !isActive && "text-red-500",
                    tabsTriggerClassName,
                  )}
                  onClick={handleClick}
                >
                  {item.iconBefore}
                  {item.label}
                  {item.iconAfter}
                </button>
              );
            })}
          </div>
        </div>

        {renderTabContent ? (
          <div
            className={cn(
              "flex-1",
              "mt-4",
              isVertical && "md:mt-0 md:ml-4",
              tabsContentClassName,
            )}
          >
            {renderTabContent(currentTab)}
          </div>
        ) : (
          <div
            className={cn(
              "w-full",
              "mt-4",
              isVertical && "md:mt-0 md:ml-4",
              tabsContentClassName,
            )}
          >
            {React.Children.map(children, (child) => {
              if (React.isValidElement(child)) {
                const childElement =
                  child as React.ReactElement<TabsChildProps>;
                return React.cloneElement(childElement, {
                  activeTab: currentTab,
                  className: cn(childElement.props.className),
                });
              }
              return child;
            })}
          </div>
        )}
      </div>
    </div>
  );
}

export function TabsContent({
  value,
  activeTab,
  className,
  children,
}: {
  value: string;
  activeTab?: string;
  className?: string;
  children: React.ReactNode;
}) {
  if (activeTab !== value) {
    return null;
  }

  return (
    <div
      className={cn("w-full", className)}
      role="tabpanel"
      id={`tabpanel-${value}`}
      aria-labelledby={`tab-${value}`}
    >
      {children}
    </div>
  );
}

// Re-export the base components for more flexibility when needed
export { BaseTabs, BaseTabsList, BaseTabsTrigger, BaseTabsContent };
