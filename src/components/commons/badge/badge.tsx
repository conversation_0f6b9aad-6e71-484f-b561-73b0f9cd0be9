import * as React from "react";
import { type VariantProps } from "class-variance-authority";
import {
  Badge as BaseBadge,
  badgeVariants as baseBadgeVariants,
} from "@/components/commons/base/badge";

/**
 * Badge component for displaying status, counts, or labels.
 *
 * @example
 * ```tsx
 * // Default badge
 * <Badge>Default</Badge>
 *
 * // Secondary badge
 * <Badge variant="secondary">Secondary</Badge>
 *
 * // With icon
 * <Badge>
 *   <CheckIcon />
 *   Success
 * </Badge>
 * ```
 */
export interface BadgeProps
  extends React.ComponentProps<"span">,
    VariantProps<typeof baseBadgeVariants> {
  asChild?: boolean;
}

const Badge = ({ ...props }: BadgeProps) => {
  return <BaseBadge {...props} />;
};

const badgeVariants = baseBadgeVariants;

export { Badge, badgeVariants };
