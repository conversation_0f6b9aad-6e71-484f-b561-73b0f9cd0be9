import { type Language } from "@store/useLanguageStore";
import Cookies from "js-cookie";

import { LANGUAGE_OPTIONS } from "./constants";
import type { LanguageOption } from "./types";

export const getCurrentLanguageOption = (
  currentLanguage: Language,
): LanguageOption => {
  return (
    LANGUAGE_OPTIONS.find((lang) => lang.code === currentLanguage) ||
    LANGUAGE_OPTIONS[0]
  );
};

export const handleLanguageChange = (
  lang: Language,
  setLanguage: (lang: Language) => void,
  setIsOpen: (isOpen: boolean) => void,
): void => {
  setLanguage(lang);

  Cookies.set("NEXT_LOCALE", lang, { expires: 365 });
  Cookies.set("preferredLanguage", lang, { expires: 365 });

  if (typeof window !== "undefined") {
    localStorage.setItem("preferredLanguage", lang);
  }

  setIsOpen(false);
};
