import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@base/card";
import { But<PERSON> } from "@base/button";
import { InputField } from "@/components/commons/form/input-field";

import { useCartItems } from "@services/api/auth/cart/cart-service";
import { Spinner } from "@/components/commons/loading-spinner";
import type { CartItem } from "@/pages/checkout/types";

interface ShoppingCartProps {
  className?: string;
  showTitle?: boolean;
}

const ShoppingCart: React.FC<ShoppingCartProps> = ({
  className = "",
  showTitle = true
}) => {
  const [discountCode, setDiscountCode] = useState("");
  const { data: cartsResponse, isLoading, isError } = useCartItems();

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center p-6">
          <Spinner size="medium" />
        </CardContent>
      </Card>
    );
  }

  if (isError || !cartsResponse?.results) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <p className="text-center text-gray-500">ไม่สามารถโหลดข้อมูลตะกร้าได้</p>
        </CardContent>
      </Card>
    );
  }

  const cartItems: CartItem[] = cartsResponse.results;
  const subtotal = cartItems.reduce((sum, item) => 
    sum + (item.product_variant.price * item.quantity), 0
  );
  const shipping = 50; // Default shipping cost
  const discount = 0; // Will be calculated based on discount code
  const total = subtotal + shipping - discount;

  const handleApplyDiscount = () => {
    // TODO: Implement discount code logic
    console.log("Applying discount code:", discountCode);
  };

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader>
          <CardTitle>สรุปคำสั่งซื้อ</CardTitle>
        </CardHeader>
      )}
      <CardContent className="space-y-4">
        {/* Cart Items */}
        <div className="space-y-3">
          {cartItems.map((item) => (
            <div key={item.id} className="flex gap-3">
              <div className="relative">
                <img
                  src={item.product_variant.first_image_url}
                  alt={item.product_variant.product_name}
                  className="h-16 w-16 rounded-md object-cover"
                />
                <span className="absolute -top-2 -right-2 bg-gray-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {item.quantity}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium truncate">
                  {item.product_variant.product_name}
                </h4>
                {item.product_variant.option_values.length > 0 && (
                  <p className="text-xs text-gray-500">
                    {item.product_variant.option_values
                      .map(opt => `${opt.option}: ${opt.value}`)
                      .join(", ")}
                  </p>
                )}
              </div>
              <div className="text-sm font-medium">
                ฿{(item.product_variant.price * item.quantity).toLocaleString()}
              </div>
            </div>
          ))}
        </div>

        {/* Discount Code */}
        <div className="border-t pt-4">
          <div className="flex gap-2">
            <InputField
              id="discount-code"
              label=""
              placeholder="รหัสส่วนลด หรือ Gift card"
              value={discountCode}
              onChange={(e) => setDiscountCode(e.target.value)}
              className="flex-1"
            />
            <Button 
              variant="outlined" 
              onClick={handleApplyDiscount}
              disabled={!discountCode.trim()}
              className="shrink-0"
            >
              ใช้
            </Button>
          </div>
        </div>

        {/* Summary */}
        <div className="border-t pt-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span>Subtotal</span>
            <span>฿{subtotal.toLocaleString()}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Shipping</span>
            <span>฿{shipping.toLocaleString()}</span>
          </div>
          {discount > 0 && (
            <div className="flex justify-between text-sm text-green-600">
              <span>Discount</span>
              <span>-฿{discount.toLocaleString()}</span>
            </div>
          )}
          <div className="border-t border-black pt-2">
            <div className="flex justify-between font-semibold">
              <span>Total</span>
              <span>฿{total.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export { ShoppingCart };
export default ShoppingCart;
