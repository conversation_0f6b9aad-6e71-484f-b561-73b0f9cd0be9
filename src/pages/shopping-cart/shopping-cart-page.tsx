import { useTranslation } from "@hooks/useTranslation.ts";
import React, { useEffect } from "react";
import QuantitySelector from "@base/quantity-selector.tsx";
import { Trash2 } from "lucide-react";
import {
  useCartItems,
  useDeleteFromCart,
  useUpdateCartItem,
} from "@services/api/auth/cart/cart-service.ts";
import { useQueryClient } from "@tanstack/react-query";

const ShoppingCartPage = () => {
  const { t } = useTranslation();
  const { data: cartsResponse, error, isError, isLoading } = useCartItems();
  useEffect(() => {
    console.log(cartsResponse);
  }, [cartsResponse]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Error: {error.message}</div>;
  }
  const carts = cartsResponse!.results;

  return (
    <>
      <div className="flex flex-col px-4 py-24">
        <h1 className={`mb-6 text-[32px]`}>{t("cart.title")}</h1>
        <hr className={`mb-4`} />
        <div>
          {carts.map((cart) => (
            <CartItem
              key={cart.id}
              id={cart.id}
              name={cart.product_variant.product_name}
              amount={cart.quantity}
              variant={cart.product_variant.sku}
              variantId={cart.product_variant.id}
              price={cart.product_variant.price}
              image={cart.product_variant.first_image_url}
            />
          ))}
        </div>
      </div>
    </>
  );
};

interface CartProps {
  id: number;
  amount: number;
  variant: string;
  variantId: number;
  name: string;
  price: number;
  image: string;
}

const CartItem: React.FC<CartProps> = ({
  id,
  name,
  image,
  variant,
  variantId,
  price,
  amount,
}) => {
  const queryClient = useQueryClient();
  const invalidateCart = () => {
    queryClient.invalidateQueries({ queryKey: ["cart-items"] });
  };
  const updateCart = useUpdateCartItem(invalidateCart);
  const deleteFromCart = useDeleteFromCart(invalidateCart);

  const handleMinus = (amount: number) => {
    updateCart.mutate({
      id,
      request: {
        product_variant_id: variantId,
        quantity: amount - 1,
      },
    });
  };

  const handlePlus = (amount: number) => {
    updateCart.mutate({
      id,
      request: {
        product_variant_id: variantId,
        quantity: amount + 1,
      },
    });
  };

  const handleDelete = () => {
    deleteFromCart.mutate(id);
  };

  return (
    <>
      <div className={`flex flex-row items-center`}>
        <div className="mr-4 h-[96px] w-[96px]">
          <img
            src={image}
            alt={name}
            className="h-full w-full rounded-lg object-cover transition-transform duration-300 ease-in-out hover:scale-105"
          />
        </div>
        <div className={`flex w-64 flex-col`}>
          <div className="mb-1">
            <h3 className="text-primary-dark truncate text-[16px] font-semibold">
              {name}
            </h3>
          </div>
          <div className={`mb-2 flex flex-row items-center justify-between`}>
            <div>
              <a className="text-normal rounded-lg border-1 border-[#E3CAA5] px-2 py-1 text-[12px]">
                {variant}
              </a>
            </div>
            <div>
              <h3 className="text-[16px] font-bold">฿{price}</h3>
            </div>
          </div>
          <div className="row flex items-center justify-between">
            <QuantitySelector
              amount={amount}
              isEnabled={true}
              handlePlusClick={handlePlus}
              handleMinusClick={handleMinus}
            />
            <div>
              <button className="cursor-point" onClick={() => handleDelete()}>
                <Trash2 />
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ShoppingCartPage;
