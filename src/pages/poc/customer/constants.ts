// Initial form state for creating a new customer
export const INITIAL_CUSTOMER_FORM_DATA = {
  email: "",
  description: "",
};

// Table headers for the customers table
export const CUSTOMER_TABLE_HEADERS = [
  { key: "id", label: "ID" },
  { key: "email", label: "Email" },
  { key: "description", label: "Description" },
  { key: "createdAt", label: "Created" },
  { key: "cards", label: "Cards" },
  { key: "actions", label: "Actions" },
];

// Page title and descriptions
export const PAGE_TITLE = "Customer Management";
export const CREATE_CUSTOMER_TITLE = "Create New Customer";
export const CREATE_CUSTOMER_DESCRIPTION =
  "Add a new customer to your Omise account";
export const EDIT_CUSTOMER_TITLE = "Edit Customer";
export const EDIT_CUSTOMER_DESCRIPTION = "Update customer information";
export const CUSTOMERS_LIST_TITLE = "Customers";
export const CUSTOMERS_LIST_DESCRIPTION = "Manage your Omise customers";
export const NO_CUSTOMERS_MESSAGE =
  "No customers found. Create your first customer above.";

// Button labels
export const CREATE_BUTTON_LABEL = "Create Customer";
export const UPDATE_BUTTON_LABEL = "Update Customer";
export const CANCEL_BUTTON_LABEL = "Cancel";
export const REFRESH_BUTTON_LABEL = "Refresh";
export const EDIT_BUTTON_LABEL = "Edit";
export const DELETE_BUTTON_LABEL = "Delete";

// Confirmation messages
export const DELETE_CONFIRMATION_MESSAGE =
  "Are you sure you want to delete this customer?";
