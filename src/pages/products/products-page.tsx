import { useTranslation } from "@/hooks/useTranslation";
import React, { useEffect, useState } from "react";
import { CheckIcon, ChevronDownIcon, ShoppingCart, Funnel } from "lucide-react";
import "./style.css";
import Modal from "@commons/modal";
import { useProducts } from "@services/api/product-service.ts";
import { useNavigate } from "react-router-dom";

interface ProductCardProps {
  id: number;
  image: string;
  name: string;
  description: string;
  price: number;
}

const ProductCard: React.FC<ProductCardProps> = ({
  id,
  name,
  image,
  description,
  price,
}) => {
  const navigate = useNavigate();

  return (
    <div
      onClick={() => navigate(`/products/${id}`)}
      className="flex cursor-pointer flex-col overflow-hidden rounded-xl px-1 transition-shadow duration-300 hover:shadow-xl md:max-w-100"
    >
      <div className="h-78 w-full overflow-hidden rounded-xl bg-gray-100">
        <img src={image} alt={name} className="h-full w-full object-cover" />
      </div>
      <div className="text-primary-dark flex-grow pt-4 text-left text-lg">
        <div>
          <h3 className="font-bold lg:text-xl">{name}</h3>
        </div>
        <p className="mt-1 truncate text-lg font-light">{description}</p>
      </div>
      <div className="flex flex-row items-center justify-between">
        <p className="font-bold lg:text-2xl">{price}฿</p>
        <div>
          <button className="bg-bg-main border-primary-dark text-primary-dark hover:bg-primary-dark flex rounded-full border-1 p-2.5 transition-all duration-200 hover:text-white">
            <ShoppingCart size={20} className="cursor-pointer" />
          </button>
        </div>
      </div>
    </div>
  );
};

const ProductsPage = () => {
  const { t } = useTranslation();
  const options = [
    {
      key: "min_price",
      value: t("filter.min_price"),
    },
    {
      key: "artist",
      value: t("filter.artist"),
    },
    {
      key: "create_at",
      value: t("filter.create_at"),
    },
  ];
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(options[1]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const {
    data: productResponse,
    isError,
    isLoading,
    error,
  } = useProducts({ ordering: selectedOption.key });
  useEffect(() => {
    console.log(productResponse);
  }, [productResponse, selectedOption.key]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Error: {error.message}</div>;
  }
  const products = productResponse!.data.results;

  const handleSelect = (option: { key: string; value: string }) => {
    setSelectedOption(option);
    setIsOpen(false);
  };

  return (
    <div className="pb-20">
      <div className="flex flex-col justify-between px-8 pt-28 lg:pt-40 2xl:flex-row 2xl:items-center">
        <h1 className="text-3xl xl:text-4xl">{t("products.title")}</h1>

        <div className="mt-8 flex flex-row items-center gap-4 2xl:mt-0">
          <h1 className="text-xl xl:text-2xl">เรียงตาม</h1>
          <div className="relative w-68 text-left">
            <div>
              <button
                type="button"
                className="inline-flex w-full items-center justify-between rounded-md border border-gray-300 bg-white px-4 py-2 text-lg text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none xl:text-xl"
                onClick={() => setIsOpen(!isOpen)}
              >
                {t(selectedOption.value)}
                <ChevronDownIcon
                  className="-mr-1 ml-2 h-5 w-5"
                  aria-hidden="true"
                />
              </button>
            </div>

            {isOpen && (
              <div className="ring-opacity-5 bg-secondary absolute right-0 z-10 mt-2 w-full origin-top-right rounded-md shadow-lg focus:outline-none">
                <div className="py-1">
                  {options.map((option) => (
                    <a
                      key={option.key}
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        handleSelect(option);
                      }}
                      className={`flex items-center px-4 py-2 text-base 2xl:text-lg ${
                        selectedOption.key === option.key
                          ? "bg-[#EFEBE4] text-gray-900"
                          : "text-gray-700"
                      } hover:bg-gray-100`}
                    >
                      {selectedOption.key === option.key && (
                        <CheckIcon className="mr-3 h-5 w-5" />
                      )}
                      <span
                        className={
                          selectedOption.key === option.key ? "" : "ml-8"
                        }
                      >
                        {option.value}
                      </span>
                    </a>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <hr className="mt-6 2xl:mt-8" />
      <section className="flex flex-col items-center px-8 2xl:mt-12 2xl:flex-row 2xl:items-start 2xl:gap-8">
        {/*Hidden if desktop*/}
        <div className="mt-8 w-full 2xl:hidden">
          <button
            onClick={() => setIsModalOpen(true)}
            className="bg-secondary text-primary-dark w-full rounded-2xl py-3 text-lg shadow-md md:px-2 md:py-2 md:text-2xl lg:px-3 lg:py-4 lg:text-2xl 2xl:text-4xl"
          >
            <Funnel size={20} className="inline" />
            {t("products.filter.title")}
          </button>
          <Modal onClose={() => setIsModalOpen(false)} isOpen={isModalOpen}>
            <Filter />
          </Modal>
        </div>

        <div className="hidden 2xl:contents">
          <Filter />
        </div>

        <div className="mt-8 grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-4 xl:grid-cols-3 2xl:mt-0 2xl:gap-8">
          {products.map((product) => (
            <>
              <ProductCard
                id={product.id}
                name={product.name}
                description={product.description}
                image={product.images[0]?.image}
                price={product.variants[0].price}
              />
            </>
          ))}
        </div>
      </section>
    </div>
  );
};

const Filter = () => {
  const { t } = useTranslation();

  const [selectedList, setSelectedList] = useState<string[]>([]);

  const handleCheckboxChange = (value: string) => {
    setSelectedList((prevSelectedList) => {
      if (prevSelectedList.includes(value)) {
        return prevSelectedList.filter((item) => item !== value);
      } else {
        return [...prevSelectedList, value];
      }
    });
  };

  const handleOnClear = () => {
    setSelectedList([]);
  };

  const options = [
    {
      name: "แบม",
      value: "bam",
    },
    {
      name: "มินากิ",
      value: "minagi",
    },
    {
      name: "อิง",
      value: "ing",
    },
    {
      name: "การ์ตูน",
      value: "cartoon",
    },
    {
      name: "บี",
      value: "bee",
    },
    {
      name: "เยียร์",
      value: "year",
    },
    {
      name: "ปีเตอร์",
      value: "peter",
    },
    {
      name: "กอล์ฟ",
      value: "golf",
    },
  ];

  return (
    <>
      <section className="bg-secondary w-100 rounded-2xl p-4 shadow-lg">
        <h3 className="mb-5 text-xl font-bold">{t("products.filter.title")}</h3>
        <h3 className="mb-5 text-xl">{t("products.filter.category")}</h3>
        <div className="mb-6 grid grid-cols-2 gap-6">
          {options.map((option) => (
            <div key={option.value} className="flex items-center">
              <input
                type="checkbox"
                id={`checkbox-${option.value}`}
                value={option.value}
                // The checkbox is checked if its value is in the selectedList array
                checked={selectedList.includes(option.value)}
                // Call the handler function on change
                onChange={() => handleCheckboxChange(option.value)}
                className="cb h-5 w-5 cursor-pointer rounded-2xl border-black"
              />
              <label
                htmlFor={`checkbox-${option.value}`}
                className="ml-3 cursor-pointer text-lg text-gray-700 select-none"
              >
                {option.name}
              </label>
            </div>
          ))}
        </div>
        <hr />

        <div className="mt-6 flex flex-row items-center gap-2">
          <button
            onClick={handleOnClear}
            className="text-primary-dark border-primary-dark hover:bg-primary-dark w-full cursor-pointer rounded-xl border-1 bg-none py-3 hover:text-white"
          >
            {t("products.filter.clear")}
          </button>

          <button className="bg-primary hover:text-primary-dark w-full cursor-pointer rounded-xl py-3 text-white">
            {t("products.filter.use-filter")}
          </button>
        </div>
      </section>
    </>
  );
};

export default ProductsPage;
