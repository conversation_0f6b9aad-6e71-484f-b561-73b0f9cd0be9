import { useTranslation } from "@/hooks/useTranslation";
import { useGoogleCallbackHandler } from "./hooks";
import "./style.css";
import { CircleArrowRight, CircleArrowLeft } from "lucide-react";
import { useEffect, useState } from "react";
import ProductCard from "@components/product";
import { useProducts } from "@services/api/product-service.ts";

const HomePage = () => {
  return (
    <div className="bg-bg-main">
      <HeroSection />
      <Recommend />
    </div>
  );
};

function HeroSection() {
  const { t } = useTranslation();
  useGoogleCallbackHandler();

  return (
    <div className="relative h-auto overflow-hidden rounded-b-2xl lg:h-[640px] 2xl:h-[800px]">
      <div className="lg:absolute lg:inset-0">
        <img
          src="public/images/hero-image.jpg"
          alt="Two people enjoying coffee"
          className="h-96 w-full rounded-b-4xl object-cover md:rounded-b-md lg:h-full"
        />
      </div>

      <div className="mt-8 pl-8 md:mt-0 lg:h-full">
        <div>
          <div className="text-5xl font-bold md:absolute md:top-32 md:text-5xl lg:text-8xl xl:text-9xl 2xl:text-[155px]">
            <h1 className="text-primary-dark lg:leading-[144px] 2xl:leading-[200px]">
              {t("home.hero.title-1")}
              <br />
              {t(t("home.hero.title-2"))}
            </h1>
          </div>
          <p className="mt-4 text-black md:absolute md:right-4 md:bottom-20 md:mt-0 md:max-w-sm md:text-right md:text-base md:text-white lg:bottom-7 xl:max-w-xl xl:text-xl 2xl:max-w-2xl 2xl:text-2xl">
            {t("home.hero.paragraph")}
          </p>
          <button className="bg-primary-dark mt-8 cursor-pointer rounded-lg px-3 py-3 text-sm text-white shadow-md transition-all duration-300 hover:bg-gray-700 md:absolute md:top-[13rem] md:px-2 md:py-2 md:text-sm lg:top-[25rem] lg:px-3 lg:py-4 lg:text-2xl 2xl:top-[32rem] 2xl:px-4 2xl:py-5 2xl:text-2xl">
            SHOP NOW
          </button>
        </div>
      </div>
    </div>
  );
}

const Recommend = () => {
  const { t } = useTranslation();
  const [currentIndex, setCurrentIndex] = useState(0);

  const { data: productResponse, isError, isLoading, error } = useProducts();
  useEffect(() => {
    console.log(productResponse);
  }, [productResponse]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Error: {error.message}</div>;
  }
  const products = productResponse!.data.results;
  console.log(products);
  const prevSlide = () => {
    const newIndex =
      currentIndex === 0 ? products.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const nextSlide = () => {
    const newIndex =
      currentIndex === products.length - 1 ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };

  return (
    <>
      <div className="mt-8 mb-16 2xl:mt-16">
        <h1 className="pl-8 text-3xl xl:text-4xl">
          {t("home.recommend.title")}
        </h1>
        <div className="flex items-center justify-between">
          <button
            onClick={prevSlide}
            className="flex-shrink-0 rounded-full p-2 text-gray-600 transition-colors duration-300 hover:bg-gray-200"
          >
            <CircleArrowLeft className="responsive-icon" />
          </button>

          {/* Carousel Viewport */}
          <div className="mt-8 grid grid-cols-1 gap-8 md:grid-cols-2 md:gap-24 lg:gap-36 2xl:mt-16">
            {/* First Card (always visible) */}
            <ProductCard
              id={products[currentIndex].id}
              name={products[currentIndex].name}
              short_text={products[currentIndex].short_text}
              image={products[currentIndex].images[0]?.image}
            />

            {/* Second Card (visible on medium screens and up) */}
            <div className="hidden md:block">
              <ProductCard
                id={products[(currentIndex + 1) % products?.length].id}
                name={products[(currentIndex + 1) % products.length].name}
                short_text={
                  products[(currentIndex + 1) % products.length].short_text
                }
                image={
                  products[(currentIndex + 1) % products.length]?.images[0]
                    ?.image
                }
              />
            </div>
          </div>

          <button
            onClick={nextSlide}
            className="flex-shrink-0 rounded-full p-2 text-gray-600 transition-colors duration-300 hover:bg-gray-200"
          >
            <CircleArrowRight className="responsive-icon" />
          </button>
        </div>
        <div className="mt-8 mb-8 flex items-center justify-center 2xl:mt-16 2xl:mb-16">
          <button className="bg-bg-main border-primary-dark text-primary-dark cursor-pointer rounded-lg border-1 px-3 py-3 text-lg shadow-md transition-all duration-300 hover:bg-gray-700 hover:text-white md:px-2 md:py-2 md:text-2xl lg:px-3 lg:py-4 lg:text-2xl 2xl:text-3xl">
            {t("home.recommend.all")}
          </button>
        </div>
      </div>
    </>
  );
};

export default HomePage;
