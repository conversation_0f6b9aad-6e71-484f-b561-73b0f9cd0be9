import { type InternalAxiosRequestConfig } from "axios";

export interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
  url?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
  password_confirm: string;
  first_name: string;
  last_name: string;
  dob: string;
  phone: string;
}

export interface UpdateUserRequest {
  first_name: string;
  last_name: string;
  password: string;
  password_confirm: string;
  dob: string;
  phone: string;
}

export interface UpdateUserResponse {
  first_name: string;
  last_name: string;
  password: string;
  password_confirm: string;
  dob: string;
  phone: string;
}

export interface AuthResponse {
  message: string;
  data: {
    access: string;
    refresh: string;
    user?: {
      id: number;
      email: string;
      first_name: string;
      last_name: string;
      full_name: string;
      tier: string;
      tier_expiry: string | null;
      phone: string | null;
      avatar_url: string | null;
      age: number | null;
      gender: string | null;
      nationality: string | null;
      email_verified: boolean;
      provider: string;
      is_tier_active: boolean;
      date_joined: string;
      created_at: string;
      updated_at: string;
      username: string;
    };
  };
}

export interface GoogleAuthResponse {
  message: string;
  data: {
    google_oauth_url: string;
  };
  user?: {
    id: string;
    username: string;
    email: string;
  };
}

export interface RefreshTokenRequest {
  refresh: string;
}

export interface VerifyEmailResponse {
  success: boolean;
  message?: string;
}
