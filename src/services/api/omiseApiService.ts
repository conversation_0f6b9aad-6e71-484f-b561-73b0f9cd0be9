import type { AxiosRequestConfig, AxiosResponse } from "axios";

import omiseAxiosInstance from "./omiseAxiosInstance";

class OmiseApiService {
  static async get<T = unknown>(
    url: string,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    const response: AxiosResponse<T> = await omiseAxiosInstance.get(
      url,
      config,
    );
    return response.data;
  }

  static async post<T = unknown, D = unknown>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    const response: AxiosResponse<T> = await omiseAxiosInstance.post(
      url,
      data,
      config,
    );
    return response.data;
  }

  static async put<T = unknown, D = unknown>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    const response: AxiosResponse<T> = await omiseAxiosInstance.put(
      url,
      data,
      config,
    );
    return response.data;
  }

  static async patch<T = unknown, D = unknown>(
    url: string,
    data?: D,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    const response: AxiosResponse<T> = await omiseAxiosInstance.patch(
      url,
      data,
      config,
    );
    return response.data;
  }

  static async delete<T = unknown>(
    url: string,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    const response: AxiosResponse<T> = await omiseAxiosInstance.delete(
      url,
      config,
    );
    return response.data;
  }

  static getInstance() {
    return omiseAxiosInstance;
  }
}

export default OmiseApiService;
