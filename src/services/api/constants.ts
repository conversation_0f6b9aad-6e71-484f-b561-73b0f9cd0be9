// Auth endpoints
export const AUTH_ENDPOINTS = {
  GOOGLE: "/auth/google/",
  GOOGLE_CALLBACK: "/auth/google/callback/",
  LOGIN: "/auth/login/",
  REGISTER: "/auth/register/",
  REFRESH_TOKEN: "/auth/token/refresh/",
  VERIFY_EMAIL: "/auth/verify-email/",
  UPDATE_USER: "/user/",
};

export const PRODUCTS_ENDPOINTS = {
  PRODUCTS: "/products",
};

export const PAYMENT_METHOD_ENDPOINTS = {
  PAYMENT_METHODS: "/payment-methods",
  CARDS: "/payment-methods/cards",
  TRUEMONEY: "/payment-methods/truemoney",
  ALIPAY: "/payment-methods/alipay",
  CLEAR_DEFAULT: "/payment-methods/clear-default",
  SET_DEFAULT: (id: string) => `/payment-methods/${id}/set-default`,
  CREATE_PAYMENT: "/payment/cards",
};

export const ADDRESS_ENDPOINTS = {
  ADDRESSES: "/user/addresses/",
  ADDRESS_BY_ID: (id: number) => `/user/addresses/${id}/`,
  SET_DEFAULT: (id: number) => `/user/addresses/${id}/set-default/`,
};

export const OMISE_PROXY_ENDPOINTS = {
  CUSTOMER_CARDS: (customerId: string) =>
    `/omise/customers/${customerId}/cards`,
  CUSTOMER_CARD: (customerId: string, cardId: string) =>
    `/omise/customers/${customerId}/cards/${cardId}`,
  CUSTOMER: "/omise/customers",
  CUSTOMER_BY_ID: (customerId: string) => `/omise/customers/${customerId}`,
};

export const CARTS_ENDPOINTS = {
  CART: "/cart/",
  CART_ITEMS: "/cart-items/",
};
