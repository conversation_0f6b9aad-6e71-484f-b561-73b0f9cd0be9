import { OmiseApiService } from "@/services/api";
import { OMISE_PROXY_ENDPOINTS } from "@/services/api/constants";
import type { Customer } from "@/types/customer";
import type { OmiseCustomer, OmiseCustomersList } from "@/types/omise";

class CustomerService {
  /**
   * Create a customer
   * @param email - The customer's email
   * @param description - A description for the customer
   * @param card - Optional card token to attach to the customer
   * @returns Promise that resolves to the created customer
   */
  async createCustomer(
    email: string,
    description: string,
    card?: string,
  ): Promise<Customer> {
    try {
      const data: { email: string; description: string; card?: string } = {
        email,
        description,
      };

      if (card) {
        data.card = card;
      }

      const response = await OmiseApiService.post(
        OMISE_PROXY_ENDPOINTS.CUSTOMER,
        data,
      );
      return this.mapOmiseCustomerToCustomer(response as OmiseCustomer);
    } catch (error) {
      console.error("Error creating customer:", error);
      throw new Error(`Failed to create customer: ${(error as Error).message}`);
    }
  }

  /**
   * Get a customer by ID
   * @param customerId - The Omise customer ID
   * @returns Promise that resolves to the customer
   */
  async getCustomer(customerId: string): Promise<Customer> {
    try {
      const response = await OmiseApiService.get(
        OMISE_PROXY_ENDPOINTS.CUSTOMER_BY_ID(customerId),
      );
      return this.mapOmiseCustomerToCustomer(response as OmiseCustomer);
    } catch (error) {
      console.error("Error fetching customer:", error);
      throw new Error(`Failed to fetch customer: ${(error as Error).message}`);
    }
  }

  /**
   * Update a customer
   * @param customerId - The Omise customer ID
   * @param data - The data to update
   * @returns Promise that resolves to the updated customer
   */
  async updateCustomer(
    customerId: string,
    data: { email?: string; description?: string; card?: string },
  ): Promise<Customer> {
    try {
      const response = await OmiseApiService.patch(
        OMISE_PROXY_ENDPOINTS.CUSTOMER_BY_ID(customerId),
        data,
      );
      return this.mapOmiseCustomerToCustomer(response as OmiseCustomer);
    } catch (error) {
      console.error("Error updating customer:", error);
      throw new Error(`Failed to update customer: ${(error as Error).message}`);
    }
  }

  /**
   * Delete a customer
   * @param customerId - The Omise customer ID
   * @returns Promise that resolves when the customer is deleted
   */
  async deleteCustomer(customerId: string): Promise<void> {
    try {
      await OmiseApiService.delete(
        OMISE_PROXY_ENDPOINTS.CUSTOMER_BY_ID(customerId),
      );
    } catch (error) {
      console.error("Error deleting customer:", error);
      throw new Error(`Failed to delete customer: ${(error as Error).message}`);
    }
  }

  /**
   * List all customers
   * @param limit - Optional limit for the number of customers to return
   * @param offset - Optional offset for pagination
   * @returns Promise that resolves to the list of customers
   */
  async listCustomers(
    limit?: number,
    offset?: number,
  ): Promise<{ data: Customer[]; total: number }> {
    try {
      let url = OMISE_PROXY_ENDPOINTS.CUSTOMER;
      const params = new URLSearchParams();

      if (limit) {
        params.append("limit", limit.toString());
      }

      if (offset) {
        params.append("offset", offset.toString());
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await OmiseApiService.get(url);

      const omiseResponse = response as OmiseCustomersList;
      return {
        data: omiseResponse.data.map((customer) =>
          this.mapOmiseCustomerToCustomer(customer),
        ),
        total: omiseResponse.total,
      };
    } catch (error) {
      console.error("Error listing customers:", error);
      throw new Error(`Failed to list customers: ${(error as Error).message}`);
    }
  }

  /**
   * Map Omise customer response to our Customer type
   * @param omiseCustomer - The Omise customer response
   * @returns Customer object
   */
  private mapOmiseCustomerToCustomer(omiseCustomer: OmiseCustomer): Customer {
    return {
      id: omiseCustomer.id,
      email: omiseCustomer.email,
      description: omiseCustomer.description,
      createdAt: new Date(omiseCustomer.created_at).toISOString(),
      cards: omiseCustomer.cards?.data || [],
      defaultCard: omiseCustomer.default_card || undefined,
      metadata: omiseCustomer.metadata || {},
    };
  }
}

export const customerService = new CustomerService();
