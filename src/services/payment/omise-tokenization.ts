import { SPACE_REGEX } from "@/constants/regex";

declare global {
  interface Window {
    Omise: {
      setPublicKey: (key: string) => void;
      createToken: (
        type: string,
        data: OmiseCardData,
        callback: (statusCode: number, response: OmiseResponse) => void,
      ) => void;
    };
  }
}

interface OmiseCardData {
  name: string;
  number: string;
  expiration_month: string;
  expiration_year: string;
  security_code: string;
}

interface OmiseResponse {
  object: string;
  id?: string;
  message?: string;
}

const initializeOmise = (): Promise<void> => {
  const publicKey = import.meta.env.VITE_OMISE_PUBLIC_KEY;

  if (window.Omise) {
    window.Omise.setPublicKey(publicKey);
    return Promise.resolve();
  }

  return new Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.src = "https://cdn.omise.co/omise.js";
    script.onload = () => {
      window.Omise.setPublicKey(publicKey);
      resolve();
    };
    script.onerror = () => {
      reject(new Error("Failed to load Omise.js"));
    };
    document.head.appendChild(script);
  });
};

const createCardToken = async (cardData: {
  holderName: string;
  cardNumber: string;
  expiryMonth: string;
  expiryYear: string;
  cvv: string;
}): Promise<string> => {
  await initializeOmise();

  return new Promise((resolve, reject) => {
    if (!window.Omise) {
      reject(new Error("Omise.js is not loaded"));
      return;
    }

    const card: OmiseCardData = {
      name: cardData.holderName,
      number: cardData.cardNumber.replace(SPACE_REGEX, ""),
      expiration_month: cardData.expiryMonth,
      expiration_year: cardData.expiryYear,
      security_code: cardData.cvv,
    };

    window.Omise.createToken(
      "card",
      card,
      (_statusCode: number, response: OmiseResponse) => {
        if (response.object === "error" || !response.id) {
          reject(new Error(response.message || "Failed to create token"));
        } else {
          resolve(response.id);
        }
      },
    );
  });
};

export const omiseTokenizationService = {
  createCardToken,
};
