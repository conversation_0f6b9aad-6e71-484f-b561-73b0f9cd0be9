import { OmiseApiService } from "@/services/api";
import { OMISE_PROXY_ENDPOINTS } from "@/services/api/constants";
import type { OmiseCard, OmiseCardsList } from "@/types/omise";

/**
 * Get all cards for a customer
 * @param customerId - The Omise customer ID
 * @returns Promise that resolves to the list of cards
 */
const getCustomerCards = async (
  customerId: string,
): Promise<OmiseCardsList> => {
  try {
    return await OmiseApiService.get(
      OMISE_PROXY_ENDPOINTS.CUSTOMER_CARDS(customerId),
    );
  } catch (error) {
    console.error("Error fetching customer cards:", error);
    throw new Error(
      `Failed to fetch customer cards: ${(error as Error).message}`,
    );
  }
};

/**
 * Get a specific card for a customer
 * @param customerId - The Omise customer ID
 * @param cardId - The Omise card ID
 * @returns Promise that resolves to the card
 */
const getCustomerCard = async (
  customerId: string,
  cardId: string,
): Promise<OmiseCard> => {
  try {
    return await OmiseApiService.get(
      OMISE_PROXY_ENDPOINTS.CUSTOMER_CARD(customerId, cardId),
    );
  } catch (error) {
    console.error("Error fetching customer card:", error);
    throw new Error(
      `Failed to fetch customer card: ${(error as Error).message}`,
    );
  }
};

/**
 * Add a credit card to a customer
 * @param customerId - The Omise customer ID
 * @param token - The Omise token
 * @returns Promise that resolves to the created card
 */
const addCardToCustomer = async (customerId: string, token: string) => {
  try {
    return await OmiseApiService.post(
      OMISE_PROXY_ENDPOINTS.CUSTOMER_CARDS(customerId),
      {
        card: token,
      },
    );
  } catch (error) {
    console.error("Error adding card to customer:", error);
    throw new Error(
      `Failed to add card to customer: ${(error as Error).message}`,
    );
  }
};

/**
 * Create a customer
 * @param email - The customer's email
 * @param description - A description for the customer
 * @returns Promise that resolves to the created customer
 */
const createCustomer = async (email: string, description: string) => {
  try {
    return await OmiseApiService.post(OMISE_PROXY_ENDPOINTS.CUSTOMER, {
      email,
      description,
    });
  } catch (error) {
    console.error("Error creating customer:", error);
    throw new Error(`Failed to create customer: ${(error as Error).message}`);
  }
};

/**
 * Get all payment methods for a customer
 * @param customerId - The Omise customer ID
 * @returns Promise that resolves to the list of payment methods
 */
const getCustomerPaymentMethods = async (customerId: string) => {
  try {
    // For now, we only support cards as payment methods in Omise
    const cards = await getCustomerCards(customerId);
    return cards;
  } catch (error) {
    console.error("Error fetching customer payment methods:", error);
    throw new Error(
      `Failed to fetch customer payment methods: ${(error as Error).message}`,
    );
  }
};

/**
 * Delete a card from a customer
 * @param customerId - The Omise customer ID
 * @param cardId - The Omise card ID
 * @returns Promise that resolves when the card is deleted
 */
const deleteCustomerCard = async (customerId: string, cardId: string) => {
  try {
    return await OmiseApiService.delete(
      OMISE_PROXY_ENDPOINTS.CUSTOMER_CARD(customerId, cardId),
    );
  } catch (error) {
    console.error("Error deleting customer card:", error);
    throw new Error(
      `Failed to delete customer card: ${(error as Error).message}`,
    );
  }
};

export const omiseApiService = {
  getCustomerCards,
  getCustomerCard,
  addCardToCustomer,
  createCustomer,
  getCustomerPaymentMethods,
  deleteCustomerCard,
};
