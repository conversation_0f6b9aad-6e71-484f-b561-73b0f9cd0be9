{"name": "mojo-muse-super-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "bun run vite", "build": "bun run tsc -b && bun run vite build", "lint": "bun run eslint .", "fix-lint": "bun run eslint . --fix", "preview": "bun run vite preview", "format": "bun run prettier --write .", "format:check": "bun run prettier --check ."}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-brands-svg-icons": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/react-fontawesome": "^0.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-query": "^5.28.4", "@tanstack/react-query-devtools": "^5.28.4", "@types/js-cookie": "^3.0.6", "@types/node": "^22.15.21", "axios": "^1.6.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-flag-icons": "^1.5.19", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "gsap": "^3.13.0", "js-cookie": "^3.0.5", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "omise": "^1.0.0", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-phone-input-2": "^2.15.1", "react-router-dom": "^7.6.1", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4.1.7", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}