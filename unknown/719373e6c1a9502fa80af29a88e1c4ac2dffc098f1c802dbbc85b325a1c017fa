import { useMutation, useQuery } from "@tanstack/react-query";
import Cookies from "js-cookie";

import { CARTS_ENDPOINTS } from "@/services";

import ApiService from "@services/api/apiService.ts";
import type {
  ApiResponse,
  CartRequest,
  CartResponse,
} from "@services/api/auth/cart/types.ts";

export const useCartItems = () => {
  const token = Cookies.get("access_token");
  return useQuery({
    queryKey: ["cart-items"],
    queryFn: async () => {
      return await ApiService.get<ApiResponse>(`${CARTS_ENDPOINTS.CART_ITEMS}`);
    },
    enabled: !!token,
  });
};

export const useAddToCart = () => {
  return useMutation({
    mutationFn: async (request: CartRequest) => {
      return await ApiService.post<CartResponse>(
        CARTS_ENDPOINTS.CART_ITEMS,
        request,
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("access_token")}`,
          },
        },
      );
    },
  });
};

export const useUpdateCartItem = (onSuccess: () => void) => {
  return useMutation<CartResponse, Error, { id: number; request: CartRequest }>(
    {
      mutationFn: async ({ id, request }) => {
        return await ApiService.patch<CartResponse>(
          CARTS_ENDPOINTS.CART_ITEMS + `${id}/`,
          request,
          {
            headers: {
              Authorization: `Bearer ${Cookies.get("access_token")}`,
            },
          },
        );
      },
      onSuccess: onSuccess,
    },
  );
};

export const useDeleteFromCart = (onSuccess: () => void) => {
  return useMutation({
    mutationFn: async (id: number) => {
      return await ApiService.delete(CARTS_ENDPOINTS.CART_ITEMS + `${id}/`, {
        headers: {
          Authorization: `Bearer ${Cookies.get("token")}`,
        },
      });
    },
    onSuccess: onSuccess,
  });
};
