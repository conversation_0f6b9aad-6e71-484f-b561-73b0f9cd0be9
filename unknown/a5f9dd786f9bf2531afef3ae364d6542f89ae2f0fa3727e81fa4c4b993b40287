import { useQuery } from "@tanstack/react-query";

import type {
  ProductResponse,
  ProductsResponse,
} from "@/pages/products/type.ts";
import { PRODUCTS_ENDPOINTS } from "@/services";

import ApiService from "./apiService";

interface ProductQueryParams {
  ordering?: string;
  page?: number;
  search?: string;
}

export const useProducts = (params?: ProductQueryParams) => {
  return useQuery({
    queryKey: [PRODUCTS_ENDPOINTS.PRODUCTS, params],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, String(value));
          }
        });
      }
      const url = `${PRODUCTS_ENDPOINTS.PRODUCTS}?${queryParams.toString()}`;
      return await ApiService.get<ProductsResponse>(url);
    },
    enabled: true,
  });
};

export const useProduct = (id: string | undefined) => {
  return useQuery({
    queryKey: ["product", id],
    queryFn: async () => {
      return await ApiService.get<ProductResponse>(
        `${PRODUCTS_ENDPOINTS.PRODUCTS}/${id!}`,
      );
    },
    enabled: !!id,
  });
};
