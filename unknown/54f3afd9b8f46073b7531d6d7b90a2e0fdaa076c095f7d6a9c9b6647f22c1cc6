import { useCallback, useEffect, useState } from "react";

import useS<PERSON> from "swr";

import { customerService } from "@/services/payment/customer-service";
import type {
  Customer,
  CustomerFormData,
  CustomerUpdateFormData,
} from "@/types/customer";

const fetchCustomers = async (
  limit?: number,
  offset?: number,
): Promise<{ data: Customer[]; total: number }> => {
  return await customerService.listCustomers(limit, offset);
};

export const useCustomers = (limit?: number, offset?: number) => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);

  const { data, error, mutate } = useSWR(["/customers", limit, offset], () =>
    fetchCustomers(limit, offset),
  );

  useEffect(() => {
    if (data) {
      setCustomers(data.data);
      setTotal(data.total);
      setIsLoading(false);
    }
  }, [data]);

  const createCustomer = useCallback(
    async (formData: CustomerFormData): Promise<Customer> => {
      const newCustomer = await customerService.createCustomer(
        formData.email,
        formData.description || "",
        formData.card,
      );
      mutate();
      return newCustomer;
    },
    [mutate],
  );

  const updateCustomer = useCallback(
    async (
      customerId: string,
      formData: CustomerUpdateFormData,
    ): Promise<Customer> => {
      const updatedCustomer = await customerService.updateCustomer(
        customerId,
        formData,
      );
      mutate();
      return updatedCustomer;
    },
    [mutate],
  );

  const deleteCustomer = useCallback(
    async (customerId: string): Promise<void> => {
      await customerService.deleteCustomer(customerId);
      mutate();
    },
    [mutate],
  );

  const getCustomer = useCallback(
    async (customerId: string): Promise<Customer> => {
      try {
        return await customerService.getCustomer(customerId);
      } catch (error) {
        console.error("Error fetching customer:", error);
        throw error;
      }
    },
    [],
  );

  return {
    customers,
    total,
    isLoading,
    error,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    getCustomer,
    refresh: mutate,
  };
};
