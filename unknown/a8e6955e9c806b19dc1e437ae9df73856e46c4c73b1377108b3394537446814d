import Cookies from "js-cookie";

import type { User } from "@/constants";

/**
 * Get user profile data from localStorage
 * @returns User object or null if not found
 */
export const getUserProfile = (): User | null => {
  try {
    const userProfileString = localStorage.getItem("userProfile");
    if (!userProfileString) return null;

    const userProfile = JSON.parse(userProfileString) as User;
    return userProfile;
  } catch (error) {
    console.error("Error getting user profile from localStorage:", error);
    return null;
  }
};

export const getAuthToken = () => {
  return Cookies.get("access_token") || "";
};

/**
 * Save user profile data to localStorage
 * @param user User object to save
 */
export const saveUserProfile = (user: User): void => {
  try {
    localStorage.setItem("userProfile", JSON.stringify(user));
  } catch (error) {
    console.error("Error saving user profile to localStorage:", error);
  }
};

/**
 * Remove user profile data from localStorage
 */
export const removeUserProfile = (): void => {
  localStorage.removeItem("userProfile");
};
