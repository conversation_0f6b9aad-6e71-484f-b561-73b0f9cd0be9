import { useState } from "react";

import { format } from "date-fns";
import { useNavigate } from "react-router-dom";

import {
  formatErrorMessage,
  handleLoginSuccess,
} from "@/pages/login/helpers.ts";
import type {
  RegisterFormData,
  RegisterFormState,
} from "@/pages/register/types.ts";
import { useRegister, useUpdateUser } from "@/services";
import { getAuthToken } from "@/utils";

export const useRegisterForm = (redirectPath: string = "/") => {
  const [formState, setFormState] = useState<RegisterFormState>({
    isLoading: false,
    error: null,
    isSuccess: false,
  });

  const navigate = useNavigate();
  const registerMutation = useRegister();
  const updateUserMutation = useUpdateUser();

  const handleSubmit = async (data: RegisterFormData) => {
    try {
      setFormState({ isLoading: true, error: null, isSuccess: false });
      const token = getAuthToken();
      console.log("token", token);

      if (token) {
        console.log("token", token);
        await updateUserMutation.mutateAsync({
          password: data.password,
          password_confirm: data.confirmPassword,
          first_name: data.firstName,
          last_name: data.lastName,
          phone: data.mobileNumber,
          dob: format(data.dateOfBirth, "yyyy-MM-dd"),
        });

        setFormState({ isLoading: false, error: null, isSuccess: true });
        navigate("/");
        return;
      }

      await registerMutation.mutateAsync({
        email: data.email, // Map email to username for the API
        password: data.password,
        password_confirm: data.confirmPassword,
        first_name: data.firstName,
        last_name: data.lastName,
        phone: data.mobileNumber,
        dob: format(data.dateOfBirth, "yyyy-MM-dd"),
      });

      setFormState({ isLoading: false, error: null, isSuccess: true });

      handleLoginSuccess(navigate, redirectPath);
    } catch (error) {
      setFormState({
        isLoading: false,
        error: formatErrorMessage(error),
        isSuccess: false,
      });
    }
  };

  return {
    formState,
    handleSubmit,
  };
};
