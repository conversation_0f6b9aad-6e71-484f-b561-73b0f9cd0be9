import { useCallback, useEffect, useState } from "react";

import useS<PERSON> from "swr";

import {
  type AlipayFormData,
  type CreditCardFormData,
  type PaymentMethod,
  PaymentMethodType,
  type TrueMoneyFormData,
} from "@/types/payment-methods";

import { paymentMethodService } from "@services/payment/payment-method-service";

const fetchPaymentMethods = async (): Promise<PaymentMethod[]> => {
  return await paymentMethodService.getPaymentMethods();
};

export const usePaymentMethods = () => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [defaultMethod, setDefaultMethod] = useState<PaymentMethod | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(true);

  const { data, error, mutate } = useSWR(
    "/payment-methods",
    fetchPaymentMethods,
  );

  useEffect(() => {
    if (data) {
      const methodsArray = Array.isArray(data) ? data : [];
      setPaymentMethods(methodsArray);
      setDefaultMethod(methodsArray.find((m) => m.isDefault) || null);
      setIsLoading(false);
    }
  }, [data]);
  const addPaymentMethod = useCallback(
    async (
      type: PaymentMethodType,
      formData: CreditCardFormData | TrueMoneyFormData | AlipayFormData,
    ) => {
      let newMethod: PaymentMethod;

      switch (type) {
        case PaymentMethodType.CREDIT_CARD:
        case PaymentMethodType.DEBIT_CARD:
          newMethod = await paymentMethodService.addCreditCard(
            formData as CreditCardFormData,
          );
          break;
        case PaymentMethodType.TRUEMONEY:
          newMethod = await paymentMethodService.addTrueMoneyWallet(
            formData as TrueMoneyFormData,
          );
          break;
        case PaymentMethodType.ALIPAY:
          newMethod = await paymentMethodService.addAlipayAccount(
            formData as AlipayFormData,
          );
          break;
        default:
          throw new Error("Unsupported payment method type");
      }

      mutate();
      return newMethod;
    },
    [mutate],
  );

  const setAsDefault = useCallback(
    async (methodId: string) => {
      await paymentMethodService.setAsDefault(methodId);
      mutate();
    },
    [mutate],
  );

  const removeMethod = useCallback(
    async (methodId: string) => {
      await paymentMethodService.removePaymentMethod(methodId);
      mutate();
    },
    [mutate],
  );

  const getCustomerCards = useCallback(async (customerId: string) => {
    try {
      return await paymentMethodService.getCustomerCards(customerId);
    } catch (error) {
      console.error("Error fetching customer cards:", error);
      throw error;
    }
  }, []);

  const getCustomerCard = useCallback(
    async (customerId: string, cardId: string) => {
      try {
        return await paymentMethodService.getCustomerCard(customerId, cardId);
      } catch (error) {
        console.error("Error fetching customer card:", error);
        throw error;
      }
    },
    [],
  );

  return {
    paymentMethods,
    defaultMethod,
    isLoading,
    error,
    addPaymentMethod,
    setAsDefault,
    removeMethod,
    getCustomerCards,
    getCustomerCard,
    refresh: mutate,
  };
};
