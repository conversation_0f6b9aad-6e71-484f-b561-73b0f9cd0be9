export interface CartRequest {
  product_variant_id: number;
  quantity: number;
}

interface OptionValue {
  option: string;
  value: string;
}

interface ProductVariant {
  id: number;
  sku: string;
  price: number;
  stock_quantity: number;
  option_values: OptionValue[];
  first_image_url: string;
  product_name: string;
}

export interface CartResponse {
  id: number;
  product_variant: ProductVariant;
  quantity: number;
}

export interface ApiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: CartResponse[];
}
