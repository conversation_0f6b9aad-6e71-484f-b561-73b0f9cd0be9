import type {
  Customer,
  CustomerFormData,
  CustomerUpdateFormData,
} from "@/types/customer";
import { customerCreateSchema, customerUpdateSchema } from "@/types/customer";

// Type for the editing customer state
export interface EditingCustomer {
  id: string;
  data: CustomerUpdateFormData;
}

export type { Customer, CustomerFormData, CustomerUpdateFormData };

export { customerCreateSchema, customerUpdateSchema };
