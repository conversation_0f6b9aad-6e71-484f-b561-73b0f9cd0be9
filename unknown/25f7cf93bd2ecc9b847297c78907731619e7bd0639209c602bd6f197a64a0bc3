import { z } from "zod";

import type { OmiseCard } from "./omise";

/**
 * Customer interface representing an Omise customer
 */
export interface Customer {
  id: string;
  email: string;
  description: string;
  createdAt: string;
  cards: OmiseCard[]; // Array of card objects
  defaultCard?: string;
  metadata: Record<string, string>;
}

/**
 * Form data for creating a customer
 */
export interface CustomerFormData {
  email: string;
  description?: string;
  card?: string;
}

/**
 * Form data for updating a customer
 */
export interface CustomerUpdateFormData {
  email?: string;
  description?: string;
  card?: string;
}

/**
 * Zod schema for customer creation form validation
 */
export const customerCreateSchema = z.object({
  email: z.string().email("อีเมลไม่ถูกต้อง"),
  description: z.string().optional(),
  card: z.string().optional(),
});

/**
 * Zod schema for customer update form validation
 */
export const customerUpdateSchema = z
  .object({
    email: z.string().email("อีเมลไม่ถูกต้อง").optional(),
    description: z.string().optional(),
    card: z.string().optional(),
  })
  .refine((data) => Object.keys(data).length > 0, {
    message: "ต้องระบุข้อมูลอย่างน้อย 1 ฟิลด์",
  });
