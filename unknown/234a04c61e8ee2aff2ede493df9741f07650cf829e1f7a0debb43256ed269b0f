import { useEffect, useState } from "react";

import { useLanguageStore } from "@store/useLanguageStore";

import { type Locale, getTranslations } from "@/config/i18n-config";

import { type NestedTranslations } from "./types";

export const useTranslation = () => {
  const { currentLanguage } = useLanguageStore();
  const [translations, setTranslations] = useState<NestedTranslations>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadTranslations = async () => {
      setIsLoading(true);
      try {
        const loadedTranslations = await getTranslations(
          currentLanguage as Locale,
        );
        setTranslations(loadedTranslations);
      } catch (error) {
        console.error("Failed to load translations:", error);
        setTranslations({});
      } finally {
        setIsLoading(false);
      }
    };

    loadTranslations();
  }, [currentLanguage]);

  const t = (
    key: string,
    replacements: Record<string, string> = {},
  ): string => {
    const keys = key.split(".");

    let value: string | NestedTranslations = translations;
    for (const k of keys) {
      if (!value || typeof value !== "object") {
        return key;
      }
      value = value[k];
    }

    if (typeof value !== "string") {
      return key;
    }

    let result = value;
    Object.entries(replacements).forEach(([placeholder, replacement]) => {
      result = result.replace(new RegExp(`{${placeholder}}`, "g"), replacement);
    });

    return result;
  };

  return {
    t,
    isLoading,
    currentLanguage,
  };
};
