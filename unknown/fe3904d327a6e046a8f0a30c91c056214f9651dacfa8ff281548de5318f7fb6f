import { useAuth } from "@store/useAuthStore";
import AuthContext from "./authen-context";
import type { AuthProviderProps, AuthContextType } from "./types";

const AuthProvider = ({ children }: AuthProviderProps) => {
  const auth = useAuth();

  const typedAuth: AuthContextType = {
    ...auth,
    user: auth.user
      ? {
          id: auth.user.id.toString(),
          username: auth.user.email,
          email: auth.user.email,
        }
      : null,
  };

  return (
    <AuthContext.Provider value={typedAuth}>{children}</AuthContext.Provider>
  );
};

export default AuthProvider;
