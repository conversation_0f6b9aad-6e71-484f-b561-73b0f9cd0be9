import React, { useEffect, useState } from "react";
import DOMPurify from "dompurify";
import { useTranslation } from "@hooks/useTranslation.ts";
import "@base/style.css";
import ProductCard from "@components/product";
import { useProduct, useProducts } from "@services/api/product-service.ts";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useAddToCart } from "@services/api/auth/cart/cart-service.ts";
import type { Variant } from "@/pages/products/type.ts";
import { getAuthToken } from "@/utils";
import QuantitySelector from "@base/quantity-selector.tsx";

const ProductDetailPage = () => {
  const { id } = useParams<{ id: string }>();

  const { data: productResponse, isLoading, isError, error } = useProduct(id);

  const [variant, setVariant] = useState<Variant>();
  const [amount, setAmount] = useState<number>(0);

  useEffect(() => {
    if (productResponse) {
      const product = productResponse.data;
      if (product.variants && product.variants.length > 0) {
        setVariant(undefined);
      }
    }
  }, [productResponse]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Error: {error.message}</div>;
  }

  const product = productResponse!.data;

  if (!product) {
    return <div>Product not found.</div>;
  }

  return (
    <>
      <div className="md:32 flex flex-col p-4 py-24 md:flex-row md:gap-5 md:py-30 xl:px-40 xl:py-36">
        <div className="md:w-[50%]">
          <ProductImages images={product.images.map((i) => i.image)} />
        </div>
        <div className="mt-6 md:w-[50%]">
          <div>
            <PrimaryDetail
              name={product.name}
              description={product.description}
              variants={product.variants}
            />
          </div>
          <div className="mt-6">
            <VariantSection
              variants={product.variants}
              variant={variant}
              setVariant={setVariant}
              amount={amount}
              setAmount={setAmount}
            />
          </div>
          <div className="mt-6">
            <AddToCart
              variant={variant}
              setVariant={setVariant}
              amount={amount}
              setAmount={setAmount}
            />
          </div>
          <div className="mt-6">
            <SecondaryDetail attribute={product.attribute} />
          </div>
        </div>
      </div>
      <div className="mb-12">
        <Recommend />
      </div>
    </>
  );
};

const ProductImages = ({ images }: { images: string[] }) => {
  const [selectedImage, setSelectedImage] = useState(images[0]);
  useEffect(() => {
    setSelectedImage(images[0]);
  }, [images]);
  const handleThumbnailClick = (url: string) => {
    setSelectedImage(url);
  };

  return (
    <div className="mx-auto w-full max-w-md">
      {/* Container for the main image */}
      <div className="mb-2">
        <img
          src={selectedImage}
          alt="Main product"
          className="h-96 w-full rounded-lg object-cover"
        />
      </div>

      {/* Container for the thumbnails */}
      <div className="flex space-x-2 overflow-x-auto pb-2">
        {images.map((url, index) => (
          <div
            key={index}
            className="flex-shrink-0 cursor-pointer"
            onClick={() => handleThumbnailClick(url)}
          >
            <img
              src={url}
              alt={`Thumbnail ${index + 1}`}
              className={`h-24 w-24 rounded-md border-2 object-cover transition-all duration-300 ${
                selectedImage === url
                  ? "scale-105 border-blue-500"
                  : "border-transparent hover:border-gray-400"
              }`}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
interface PrimaryDetailProps {
  variants: {
    stock_quantity: number;
  }[];
  name: string;
  description: string;
}

const PrimaryDetail: React.FC<PrimaryDetailProps> = ({
  variants,
  name,
  description,
}) => {
  const { t } = useTranslation();
  const isInStock: boolean =
    variants.filter((v) => v.stock_quantity > 0).length > 0;
  const stockMessage = isInStock
    ? t("product-detail.isInStock")
    : t("product-detail.outOfStock");
  const sanitizedData = () => ({
    __html: DOMPurify.sanitize(description),
  });

  return (
    <>
      <div className="flex flex-col">
        <div className="mb-6">
          <a className="border-primary-light rounded-md border-1 px-3 py-1">
            {stockMessage}
          </a>
        </div>
        <div className="mb-6">
          <h1 className="text-5xl font-bold">{name}</h1>
        </div>
        <div>
          <p dangerouslySetInnerHTML={sanitizedData()}></p>
        </div>
      </div>
    </>
  );
};

interface SizeSectionProps {
  variants: Variant[];
  variant: Variant | undefined;
  setVariant: React.Dispatch<React.SetStateAction<Variant | undefined>>;
  amount: number;
  setAmount: React.Dispatch<React.SetStateAction<number>>;
}

const VariantSection: React.FC<SizeSectionProps> = ({
  variants,
  variant,
  setVariant,
  amount,
  setAmount,
}) => {
  const { t } = useTranslation();

  const handleSizeClick = (selectedVariant: Variant) => {
    setAmount(0);
    if (selectedVariant.sku == variant?.sku) {
      setVariant(undefined);
      return;
    }
    setVariant(selectedVariant);
  };

  const handlePlusClick = (amount: number) => {
    if (variant != null && variant.stock_quantity > amount) {
      setAmount(amount + 1);
    }
  };

  const handleMinusClick = (amount: number) => {
    if (amount == 0) {
      return;
    }
    setAmount(amount - 1);
  };
  return (
    <>
      <div className="flex flex-col">
        <h3 className="mb-6 font-bold">{t("product-detail.size")}</h3>
        <div className="mb-6 grid grid-cols-4 gap-4">
          {variants.map((v) => (
            <button
              key={v.sku}
              disabled={v.stock_quantity <= 0}
              onClick={() => handleSizeClick(v)}
              className={`rounded-md border px-3 py-2 text-center font-medium transition-colors duration-200 disabled:cursor-not-allowed disabled:border-gray-300 disabled:bg-gray-200 disabled:text-gray-400 ${
                v.sku == variant?.sku
                  ? "border-gray-800 bg-gray-800 text-white" // Highlighted style
                  : "border-gray-400 bg-white text-gray-800 hover:bg-gray-100" // Default style
              } `}
            >
              {v.sku}
            </button>
          ))}
        </div>
        <div className="flex flex-row items-center justify-between">
          <h3 className="text-primary-dark text-2xl font-bold">
            {`${variant != null ? "฿" + variant.price : ""}`}
          </h3>
          <QuantitySelector
            amount={amount}
            handleMinusClick={handleMinusClick}
            handlePlusClick={handlePlusClick}
            isEnabled={variant != null}
          />
        </div>
      </div>
    </>
  );
};

interface AddToCartProps {
  variant: Variant | undefined;
  setVariant: React.Dispatch<React.SetStateAction<Variant | undefined>>;
  amount: number;
  setAmount: React.Dispatch<React.SetStateAction<number>>;
}

const AddToCart: React.FC<AddToCartProps> = ({
  variant,
  setVariant,
  amount,
  setAmount,
}) => {
  const { t } = useTranslation();
  const shoppingCartMutation = useAddToCart();
  const navigate = useNavigate();
  const location = useLocation();
  const token = getAuthToken();

  const handleOnClick = () => {
    if (amount < 1 || !variant) {
      return;
    }

    //TODO handle case not logged in
    if (!token) {
      localStorage.setItem("redirectUrl", location.pathname);
      navigate("/login");
    }

    shoppingCartMutation.mutate({
      product_variant_id: variant?.id,
      quantity: amount,
    });
    setVariant(undefined);
    setAmount(0);
  };

  return (
    <>
      <button
        onClick={() => {
          handleOnClick();
        }}
        className={`w-full rounded-md py-3 ${variant ? "bg-primary cursor-pointer text-white" : "cursor bg-gray-100 text-gray-400"}`}
      >
        {t("product-detail.addToCart")}
      </button>
    </>
  );
};

interface SecondaryDetailProps {
  attribute: string;
}

const SecondaryDetail: React.FC<SecondaryDetailProps> = ({ attribute }) => {
  const sanitizedData = () => ({
    __html: DOMPurify.sanitize(attribute),
  });

  return (
    <>
      <div>
        <p dangerouslySetInnerHTML={sanitizedData()}></p>
      </div>
    </>
  );
};

const Recommend = () => {
  const { t } = useTranslation();

  const { data: productResponse, isError, isLoading, error } = useProducts();
  useEffect(() => {
    console.log(productResponse);
  }, [productResponse]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Error: {error.message}</div>;
  }
  const products = productResponse!.data.results;
  return (
    <>
      <h2 className="mb-6 text-4xl">{t("product-detail.interest")}</h2>
      <div className="flex space-x-3 overflow-x-auto pb-2">
        {products.map((product, index) => (
          <div className="flex-shrink-1">
            <ProductCard
              id={product.id}
              key={index}
              name={product.name}
              short_text={product.short_text}
              image={product.images[0]?.image}
            />
          </div>
        ))}
      </div>
    </>
  );
};

export default ProductDetailPage;
