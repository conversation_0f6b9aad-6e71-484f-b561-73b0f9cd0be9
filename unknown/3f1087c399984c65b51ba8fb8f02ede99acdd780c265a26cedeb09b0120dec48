.responsive-icon {
  width: 32px;
  height: 32px;
}

.responsive-cart {
  width: 20px;
  height: 20px;
}

@media (min-width: 600px) {
  .responsive-icon {
    width: 32px;
    height: 32px;
  }
}

@media (min-width: 768px) {
  .responsive-icon {
    width: 40px;
    height: 40px;
  }
}

@media (min-width: 1200px) {
  .responsive-icon {
    width: 42px;
    height: 42px;
  }

  .responsive-cart {
    width: 32px;
    height: 32px;
  }
}

button {
  cursor: pointer;
}

.react-tel-input .form-control {
  width: 100% !important;
}

.react-tel-input .flag-dropdown {
  border: none !important;
}

.react-tel-input .selected-flag:hover {
  background-color: transparent !important;
}

.react-tel-input .selected-flag {
  border: 1px solid #cacaca;
  border-radius: 3px 0 0 3px;
}
