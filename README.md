# Mojo Muse Super App

A modern e-commerce platform built with Vite, React, TypeScript, Tailwind CSS, and shadcn/ui.

## Features

- ⚡️ **Vite** - Lightning-fast frontend tooling
- ⚛️ **React** - A JavaScript library for building user interfaces
- 🔷 **TypeScript** - Type safety for better development experience
- 🎨 **Tailwind CSS** - A utility-first CSS framework
- 🧩 **shadcn/ui** - Accessible and customizable component library
- 🔧 **Prettier** - Code formatting for consistent style

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- Bun (recommended) or npm (v7 or higher)

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/mojo-muse-super-app.git
cd mojo-muse-super-app
```

2. Install dependencies:

```bash
# Using Bun (recommended)
bun install

# Or using npm
npm install
```

3. Start the development server:

```bash
# Using Bun
bun run dev

# Or using npm
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Environment Variables

The project uses different environment files for different environments:

- `.env` - Base environment variables (committed to git)
- `.env.local` - Local environment variables (not committed to git)
- `.env.dev` - Development environment variables
- `.env.production` - Production environment variables

To use environment variables in your code:

```tsx
// Access environment variables using import.meta.env
const apiUrl = import.meta.env.VITE_API_BASE_URL;
```

## Available Scripts

Using Bun (recommended):

- `bun run dev` - Start the development server
- `bun run build` - Build the application for production
- `bun run preview` - Preview the production build locally
- `bun run lint` - Lint the codebase
- `bun run format` - Format the codebase with Prettier
- `bun run format:check` - Check if the codebase is properly formatted

Using npm:

- `npm run dev` - Start the development server
- `npm run build` - Build the application for production
- `npm run preview` - Preview the production build locally
- `npm run lint` - Lint the codebase
- `npm run format` - Format the codebase with Prettier
- `npm run format:check` - Check if the codebase is properly formatted

## Deployment

### Nginx Configuration

The application is configured to be served through nginx as a reverse proxy on port 8080. Several nginx configuration files are provided:

- `nginx.conf` - Basic nginx configuration
- `server/nginx.conf` - Production-ready configuration with enhanced security and logging
- `docker/nginx.conf` - Docker-specific configuration

### Docker Deployment

1. Build and run with Docker:

```bash
# Build the Docker image
docker build -t mojo-muse-app .

# Run the container
docker run -p 8080:8080 mojo-muse-app
```

2. Or use Docker Compose:

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Manual Nginx Setup

1. Build the application:

```bash
bun run build
```

2. Copy the built files to your nginx document root:

```bash
sudo cp -r dist/* /usr/share/nginx/html/
```

3. Copy the nginx configuration:

```bash
sudo cp server/nginx.conf /etc/nginx/sites-available/mojo-muse
sudo ln -s /etc/nginx/sites-available/mojo-muse /etc/nginx/sites-enabled/
```

4. Test and reload nginx:

```bash
sudo nginx -t
sudo systemctl reload nginx
```

The application will be available at `http://localhost:8080`

## Project Structure

```
mojo-muse-super-app/
├── public/              # Static assets
├── src/
│   ├── components/      # Reusable UI components
│   │   └── commons/     # Common components
│   │       └── base/    # shadcn/ui components
│   ├── lib/             # Utility functions and helpers
│   ├── screens/         # Page components
│   ├── styles/          # Global styles
│   ├── hooks/           # Custom React hooks
│   ├── services/        # API services
│   ├── utils/           # Utility functions
│   ├── constants/       # Constants and configuration
│   ├── App.tsx          # Main application component
│   ├── main.tsx         # Entry point
│   └── index.css        # Global CSS with Tailwind directives
├── .env                 # Base environment variables
├── .env.dev             # Development environment variables
├── .env.production      # Production environment variables
├── .prettierrc          # Prettier configuration
├── components.json      # shadcn/ui configuration
├── shadcn.config.json   # Additional shadcn/ui configuration
├── tailwind.config.js   # Tailwind CSS configuration
├── tsconfig.json        # TypeScript configuration
├── tsconfig.app.json    # Application-specific TypeScript configuration
└── vite.config.ts       # Vite configuration
```

## Path Aliases

The project uses path aliases to make imports cleaner and more maintainable. These are configured in `tsconfig.app.json`:

```json
{
  "paths": {
    "@/*": ["./src/*"],
    "@components/*": ["./src/components/*"],
    "@commons/*": ["./src/components/commons/*"],
    "@base/*": ["./src/components/commons/base/*"],
    "@screens/*": ["./src/screens/*"],
    "@lib/*": ["./src/lib/*"],
    "@services/*": ["./src/services/*"],
    "@styles/*": ["./src/styles/*"],
    "@utils/*": ["./src/utils/*"],
    "@hooks/*": ["./src/hooks/*"],
    "@constants/*": ["./src/constants/*"]
  }
}
```

Usage example:

```tsx
// Instead of
import { Button } from '../../../components/commons/base/button';

// You can use
import { Button } from '@base/button';
```

## Adding New Components

You can add new shadcn/ui components using the CLI:

```bash
# Using npx
npx shadcn@latest add [component-name]

# Using Bun
bunx --bun shadcn@latest add [component-name]
```

For example, to add the Dialog component:

```bash
bunx --bun shadcn@latest add dialog
```

Components will be added to the `src/components/commons/base` directory as configured in `components.json` and `shadcn.config.json`.

## License

MIT
